"""
测试正确的SNR验证逻辑

验证正确的SNR验证：
- 信号方差：从实际生成的信号数据计算
- 噪声方差：从实际采样的噪声样本计算
- 实际SNR：信号方差 / 噪声方差
- 验证：实际SNR vs 目标SNR的差异

新增功能：
- 生成n个数据集，每次都分别使用SNR和不使用SNR生成两组数据
- 比较n次同一个数据集分别使用SNR计算噪声和不使用SNR计算噪声的实际SNR
- 打印每一次生成数据用的dag图
- 比较原始train数据、原始test数据、扰动test数据上特征X+Y 两两之间的相关性和互信息
- 比较最终XGBoost和TabPFN在两种方法上的原始train数据R2性能和原始test、扰动test上的R2性能
- 使用permutation_importance为每个数据集每种预测方法增加特征重要性分析并可视化对比结果
"""

import os
import gc
import json
import pandas as pd
import numpy as np
import concurrent.futures
import multiprocessing
import pickle
import warnings
import math
import torch
import matplotlib.pyplot as plt
import seaborn as sns
import traceback
import signal
import pdb
import ipdb
import networkx as nx
from datetime import datetime
from collections import OrderedDict

from scm_data_generator import generate_datasets
from utils_scm import draw_graph
from utils_plot import (
    ImportancePlotter,
    R2Plotter
)
from utils_test import (
    extract_serializable_scm_info,
    should_draw_dag_shared,
    calculate_pairwise_correlations_and_mi,
    process_comprehensive_data,
    save_summary_csv
)

def calculate_effective_datasets(results, r2_threshold=0.5):
    """基于R2阈值计算有效数据集"""
    if not results:
        return {}, {}

    # 按配置分组数据
    config_data = {}
    for r in results:
        config_key = r.get('use_snr', 'unknown')
        dataset_name = r.get('dataset', 'unknown')
        model_type = r.get('model_type', 'unknown')

        if config_key not in config_data:
            config_data[config_key] = {}
        if dataset_name not in config_data[config_key]:
            config_data[config_key][dataset_name] = {}

        # 提取R2值 - 使用r2_train而不是r2_test，与原始代码保持一致
        r2_train = r.get('r2_train', None)
        config_data[config_key][dataset_name][model_type] = r2_train

    # 计算有效数据集
    effective_datasets = {}
    for config_key, datasets in config_data.items():
        effective_dataset_set = set()

        # 记录该配置下的总数据集数量
        total_datasets_count = len(datasets)

        # 转换为DataFrame进行处理
        dataset_records = []
        for dataset_name, models in datasets.items():
            record = {
                'dataset': dataset_name,
                'xgb_r2': models.get('xgboost', None),
                'tabpfn_default_r2': models.get('tabpfn_default', None),
                'tabpfn_mse_r2': models.get('tabpfn_mse', None),
                'tabpfn_muzero_r2': models.get('tabpfn_muzero', None)
            }
            dataset_records.append(record)

        if not dataset_records:
            # 如果没有数据记录，设置eff_equ_all为False
            effective_datasets[config_key] = {'datasets': set(), 'eff_equ_all': False}
            continue

        df = pd.DataFrame(dataset_records)

        # 检查有效性 - 检查XGBoost是否有有效值
        xgb_valid_mask = df['xgb_r2'].notna()

        # 检查TabPFN是否有任何有效值（不为None且大于0.001）
        tabpfn_valid_mask = (
            (df['tabpfn_default_r2'].notna() & (df['tabpfn_default_r2'] > 0.001)) |
            (df['tabpfn_mse_r2'].notna() & (df['tabpfn_mse_r2'] > 0.001)) |
            (df['tabpfn_muzero_r2'].notna() & (df['tabpfn_muzero_r2'] > 0.001))
        )

        # 计算最佳TabPFN R2值
        df['best_tabpfn_r2'] = df[['tabpfn_default_r2', 'tabpfn_mse_r2', 'tabpfn_muzero_r2']].max(axis=1, skipna=True)
        df.loc[~tabpfn_valid_mask, 'best_tabpfn_r2'] = None

        # 对于有TabPFN结果的数据集，要求两个模型都满足阈值
        both_valid_mask = xgb_valid_mask & tabpfn_valid_mask
        both_valid_df = df[both_valid_mask]

        if len(both_valid_df) > 0:
            both_r2_mask = (both_valid_df['xgb_r2'] >= r2_threshold) & (both_valid_df['best_tabpfn_r2'] >= r2_threshold)
            both_effective = both_valid_df[both_r2_mask]['dataset'].tolist()
            effective_dataset_set.update(both_effective)

        # 对于只有XGBoost结果的数据集，只检查XGBoost
        only_xgb_mask = xgb_valid_mask & ~tabpfn_valid_mask
        only_xgb_df = df[only_xgb_mask]

        if len(only_xgb_df) > 0:
            xgb_r2_mask = only_xgb_df['xgb_r2'] >= r2_threshold
            xgb_effective = only_xgb_df[xgb_r2_mask]['dataset'].tolist()
            effective_dataset_set.update(xgb_effective)

        # 计算有效数据集数量并添加eff_equ_all标志
        effective_count = len(effective_dataset_set)
        eff_equ_all = (effective_count == total_datasets_count)

        # 创建包含数据集和eff_equ_all标志的字典
        effective_datasets[config_key] = {
            'datasets': effective_dataset_set,
            'eff_equ_all': eff_equ_all
        }

    return config_data, effective_datasets

from sklearn.metrics import r2_score, mean_squared_error
from sklearn.inspection import permutation_importance
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression, Lasso
import xgboost as xgb

# CatBoost导入
try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
    print("CatBoost imported successfully")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("CatBoost not available. Please install with: pip install catboost")

# LightGBM导入
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
    print("LightGBM imported successfully")
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM not available. Please install with: pip install lightgbm")



# TabPFN导入
try:
    from tabpfn import TabPFNRegressor
    TABPFN_AVAILABLE = True
    print("TabPFN imported successfully")
except ImportError:
    TABPFN_AVAILABLE = False
    print("TabPFN not available. Please install with: pip install tabpfn")

# TabPFN变体导入
try:
    from inference.regressor_mse import TabPFNRegressor_mse
    TABPFN_MSE_AVAILABLE = False
    print("TabPFNRegressor_mse imported successfully")
except ImportError:
    TABPFN_MSE_AVAILABLE = False
    print("TabPFNRegressor_mse not available")

try:
    from inference.regressor_muzero import TabPFNRegressor_muzero
    TABPFN_MUZERO_AVAILABLE = True
    print("TabPFNRegressor_muzero imported successfully")
except ImportError:
    TABPFN_MUZERO_AVAILABLE = False
    print("TabPFNRegressor_muzero not available")

import matplotlib
matplotlib.use('Agg')
# matplotlib.use('TkAgg')  # 或 'Qt5Agg'，取决于本地环境
# matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 显示中文（黑体）
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号


# 模型训练与评估
def run_model_and_eval(train_x, train_y, test_x, test_y, model_type='xgboost', device='cuda'):
    """
    训练模型并评估性能

    Args:
        train_x, train_y: 训练数据
        test_x, test_y: 测试数据
        model_type: 'xgboost', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero', 'ols', 'lasso', 'catboost', 'lightgbm'
        device: 设备类型（TabPFN使用）

    Returns:
        model, metrics_train, metrics_test, scaler (scaler为None表示该模型不需要归一化)
    """
    scaler = None  # 初始化scaler为None

    if model_type == 'ols':
        # 普通最小二乘法回归
        scaler = StandardScaler()
        train_x_scaled = scaler.fit_transform(train_x.copy())
        test_x_scaled = scaler.transform(test_x.copy())
        model = LinearRegression()
        model.fit(train_x_scaled, train_y)
        pred_train = model.predict(train_x_scaled)
        pred_test = model.predict(test_x_scaled)

    elif model_type == 'lasso':
        # LASSO回归，使用交叉验证选择最优alpha
        scaler = StandardScaler()
        train_x_scaled = scaler.fit_transform(train_x.copy())
        test_x_scaled = scaler.transform(test_x.copy())
        model = Lasso(alpha=10, random_state=0) # alpha=10, max_iter=2000
        model.fit(train_x_scaled, train_y)
        pred_train = model.predict(train_x_scaled)
        pred_test = model.predict(test_x_scaled)
        
    elif model_type == 'catboost':
        if not CATBOOST_AVAILABLE:
            raise ValueError(f"CatBoost is not available. Please install with: pip install catboost")
        # CatBoost回归配置优化
        model = CatBoostRegressor(
            random_seed=0,
            verbose=False,
            allow_writing_files=False,  # 避免生成临时文件
            thread_count=1,  # 限制线程数，避免多进程环境下的线程竞争
            task_type='GPU', 
            # iterations=100, depth=3, learning_rate=0.1  # 可根据需要调整
        )
        model.fit(train_x, train_y)
        pred_train = model.predict(train_x)
        pred_test = model.predict(test_x)

    elif model_type == 'xgboost':
        # XGBoost
        model = xgb.XGBRegressor(
            random_state=0,
            verbosity=0,
            n_jobs=1,  # 限制为单线程，避免多进程环境下的线程竞争
            tree_method='gpu_hist'
            # n_estimators=100, max_depth=3, , predictor='gpu_predictor'  # 可根据需要调整
        )
        model.fit(train_x, train_y)
        pred_train = model.predict(train_x)
        pred_test = model.predict(test_x)

    elif model_type == 'lightgbm':
        if not LIGHTGBM_AVAILABLE:
            raise ValueError(f"LightGBM is not available. Please install with: pip install lightgbm")
        # LightGBM
        warnings.filterwarnings('ignore', message='X does not have valid feature names')

        model = lgb.LGBMRegressor(
            random_state=0,
            verbosity=-1,
            force_col_wise=True,
            n_jobs=1,  # 限制线程数，避免多进程环境下的线程竞争
            # n_estimators=100, max_depth=3, learning_rate=0.1  # 可根据需要调整
        )
        model.fit(train_x, train_y)
        pred_train = model.predict(train_x)
        pred_test = model.predict(test_x)

    elif model_type in ['tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']:
        # 根据模型类型选择对应的类和权重文件
        if model_type == 'tabpfn_default':
            if not TABPFN_AVAILABLE:
                raise ValueError(f"TabPFN is not available. Please install with: pip install tabpfn")
            model_class = TabPFNRegressor
            model_path = '/root/LDM_test/tabpfn-v2-regressor.ckpt'
        elif model_type == 'tabpfn_mse':
            if not TABPFN_MSE_AVAILABLE:
                raise ValueError(f"TabPFNRegressor_mse is not available")
            model_class = TabPFNRegressor_mse
            model_path = './new_model_checkpoints/mse_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
        elif model_type == 'tabpfn_muzero':
            if not TABPFN_MUZERO_AVAILABLE:
                raise ValueError(f"TabPFNRegressor_muzero is not available")
            model_class = TabPFNRegressor_muzero
            model_path = './new_model_checkpoints/muzero_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'

        # 检查模型权重文件是否存在
        if not os.path.exists(model_path):
            print(f"Warning: Model weight file {model_path} does not exist, skipping {model_type}")
            raise FileNotFoundError(f"Model weight file {model_path} not found")

        # TabPFN对数据大小有限制，如果数据太大需要采样
        # 清理GPU内存
        if device == 'cuda':
            torch.cuda.empty_cache()
            gc.collect()
        max_samples = 10000  # TabPFN的限制
        if train_x.shape[0] > max_samples:
            indices = np.random.choice(train_x.shape[0], max_samples, replace=False)
            train_x_sample = train_x[indices]
            train_y_sample = train_y[indices]
        else:
            train_x_sample = train_x
            train_y_sample = train_y

        # 调试信息：打印数据类型和形状
        # print(f"TabPFN训练数据调试信息:")
        # print(f"  train_x_sample类型: {type(train_x_sample)}, 形状: {train_x_sample.shape}")
        # print(f"  train_y_sample类型: {type(train_y_sample)}, 形状: {train_y_sample.shape}")
        # print(f"  train_x_sample数据类型: {train_x_sample.dtype}")
        # print(f"  train_y_sample数据类型: {train_y_sample.dtype}")
        # print(f"  train_x_sample是否包含NaN: {np.isnan(train_x_sample).any()}")
        # print(f"  train_y_sample是否包含NaN: {np.isnan(train_y_sample).any()}")
        # print(f"  train_x_sample是否包含Inf: {np.isinf(train_x_sample).any()}")
        # print(f"  train_y_sample是否包含Inf: {np.isinf(train_y_sample).any()}")
        # print(f"  train_x_sample范围: [{train_x_sample.min():.6f}, {train_x_sample.max():.6f}]")
        # print(f"  train_y_sample范围: [{train_y_sample.min():.6f}, {train_y_sample.max():.6f}]")

        # 检查数据是否可序列化
        # try:
        #     pickle.dumps(train_x_sample)
        #     pickle.dumps(train_y_sample)
        #     print("  数据可以正常序列化")
        # except Exception as pickle_e:
        #     print(f"  数据序列化失败: {str(pickle_e)}")

        # # 确保数据是连续的numpy数组
        # if not train_x_sample.flags['C_CONTIGUOUS']:
        #     train_x_sample = np.ascontiguousarray(train_x_sample)
        #     print("  已将train_x_sample转换为连续数组")
        # if not train_y_sample.flags['C_CONTIGUOUS']:
        #     train_y_sample = np.ascontiguousarray(train_y_sample)
        #     print("  已将train_y_sample转换为连续数组")

        # # 确保数据类型正确
        # if train_x_sample.dtype != np.float32:
        #     train_x_sample = train_x_sample.astype(np.float32)
        #     print(f"  已将train_x_sample转换为float32")
        # if train_y_sample.dtype != np.float32:
        #     train_y_sample = train_y_sample.astype(np.float32)
        #     print(f"  已将train_y_sample转换为float32")

        try:
            print(f"  开始创建TabPFN模型，设备: {device}")

            # 添加超时机制

            def timeout_handler(signum, frame):
                raise TimeoutError("TabPFN训练超时")

            # 设置600秒超时
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(600)

            try:
                model = model_class(device=device, ignore_pretraining_limits=True, model_path=model_path)
                print(f"  {model_type}模型创建成功")

                print(f"  开始训练{model_type}模型...")
                model.fit(train_x_sample, train_y_sample)
                print(f"  {model_type}模型训练成功")

                print(f"  开始{model_type}预测...")
                pred_train = model.predict(train_x)
                pred_test = model.predict(test_x)
                print(f"  {model_type}预测完成")
            finally:
                signal.alarm(0)  # 取消超时

        except TimeoutError as timeout_e:
            print(f"  {model_type}训练超时: {str(timeout_e)}")
            print(f"  跳过{model_type}模型，继续处理其他模型...")
            # 直接抛出异常，让上层处理
            raise timeout_e
        except Exception as tabpfn_e:
            print(f"  {model_type}训练/预测出错: {str(tabpfn_e)}")
            print(f"  错误类型: {type(tabpfn_e).__name__}")
            traceback.print_exc()

            # 检查是否是序列化相关错误
            error_msg = str(tabpfn_e).lower()
            if "serialize" in error_msg or "pickle" in error_msg or "un-serialize" in error_msg:
                print("\n" + "="*80)
                print(f"检测到{model_type}序列化错误，启动调试模式")
                print("="*80)

                # 打印详细的调试信息
                print(f"错误详情: {str(tabpfn_e)}")
                print(f"数据信息:")
                print(f"  train_x_sample: shape={train_x_sample.shape}, dtype={train_x_sample.dtype}")
                print(f"  train_y_sample: shape={train_y_sample.shape}, dtype={train_y_sample.dtype}")
                print(f"  内存连续性: X_C={train_x_sample.flags['C_CONTIGUOUS']}, Y_C={train_y_sample.flags['C_CONTIGUOUS']}")

                # 尝试重新序列化测试
                try:
                    pickle.dumps(train_x_sample)
                    pickle.dumps(train_y_sample)
                    print("  数据本身可以序列化")
                except Exception as pickle_err:
                    print(f"  数据序列化测试失败: {pickle_err}")

                # 导入调试库并设置断点
                print("\n启动Python调试器...")
                try:
                    print("已导入pdb调试器")
                    print("可用的调试变量:")
                    print("  - train_x_sample: 训练特征数据")
                    print("  - train_y_sample: 训练目标数据")
                    print("  - tabpfn_e: TabPFN异常对象")
                    print("  - device: 设备信息")
                    print("输入 'c' 继续执行，'q' 退出调试器")
                    pdb.set_trace()  # 设置断点，暂停程序执行
                except ImportError:
                    print("无法导入pdb调试器")
                    # 尝试使用ipdb
                    try:

                        print("已导入ipdb调试器")
                        ipdb.set_trace()
                    except ImportError:
                        print("无法导入ipdb调试器，使用input()暂停")
                        input("按Enter键继续...")

                print("="*80)
                print("调试模式结束")
                print("="*80)

            raise tabpfn_e

    else:
        raise ValueError(f"Unsupported model type: {model_type}")

    # 计算训练集指标
    r2_train = r2_score(train_y, pred_train)
    rmse_train = np.sqrt(mean_squared_error(train_y, pred_train))
    mape_train = np.mean(np.abs((train_y - pred_train) / (train_y + 1e-8))) * 100

    # 计算测试集指标
    r2_test = r2_score(test_y, pred_test)
    rmse_test = np.sqrt(mean_squared_error(test_y, pred_test))
    mape_test = np.mean(np.abs((test_y - pred_test) / (test_y + 1e-8))) * 100

    metrics_train = {'r2': r2_train, 'rmse': rmse_train, 'mape': mape_train}
    metrics_test = {'r2': r2_test, 'rmse': rmse_test, 'mape': mape_test}

    return model, metrics_train, metrics_test, scaler

# 计算特征重要性
def calculate_feature_importance(model, test_x, test_y, feature_names, model_type='xgboost'):
    """
    计算特征重要性

    Args:
        model: 训练好的模型
        test_x, test_y: 测试数据
        feature_names: 特征名称列表
        model_type: 模型类型

    Returns:
        importance_dict: 特征重要性字典
    """
    importance_dict = {}

    # Permutation Importance（适用于所有模型）
    # 对于TabPFN模型，使用单进程避免CUDA内存问题
    n_jobs = 4 if model_type.startswith('tabpfn') else -1
    perm_importance = permutation_importance(
        model, test_x, test_y,
        n_repeats=3, random_state=42
    )
    importance_dict['permutation'] = {
        name: importance for name, importance in
        zip(feature_names, perm_importance.importances_mean)
    }

    # 模型内置重要性
    if model_type == 'xgboost':
        # XGBoost内置重要性
        importance_dict['builtin'] = {
            name: importance for name, importance in
            zip(feature_names, model.feature_importances_)
        }
    elif model_type == 'ols':
        # OLS系数绝对值作为重要性
        importance_dict['builtin'] = {
            name: abs(coef) for name, coef in
            zip(feature_names, model.coef_)
        }
    elif model_type == 'lasso':
        # LASSO系数绝对值作为重要性
        importance_dict['builtin'] = {
            name: abs(coef) for name, coef in
            zip(feature_names, model.coef_)
        }
    elif model_type == 'catboost':
        # CatBoost内置重要性
        if hasattr(model, 'feature_importances_'):
            importance_dict['builtin'] = {
                name: importance for name, importance in
                zip(feature_names, model.feature_importances_)
            }
    elif model_type == 'lightgbm':
        # LightGBM内置重要性
        if hasattr(model, 'feature_importances_'):
            importance_dict['builtin'] = {
                name: importance for name, importance in
                zip(feature_names, model.feature_importances_)
            }

    return importance_dict


# ========== 并行处理工作函数 ===========
def process_single_configuration(config_key, config_info, h_config, n, intervention_node_type,
                                intervention_value_method, custom_dag_type, model_types,
                                device, gpu_id, custom_dag_size=None, image_dir=None, shared_drawn_dag_signatures=None, avg_in_degree=(1.2, 1.7), calculate_correlation_mi=True, extract_function_weights=True):
    """
    处理单个配置的所有数据集（工作函数）

    Args:
        config_key: 配置键
        config_info: 配置信息
        h_config: 基础配置
        n: 数据集数量
        intervention_node_type: 干预节点类型
        intervention_value_method: 干预值方法
        custom_dag_type: 自定义DAG类型
        model_types: 模型类型列表
        device: 设备类型
        gpu_id: 分配的GPU ID
        avg_in_degree: 随机图的平均入度范围，格式为(min_degree, max_degree)
        calculate_correlation_mi: 是否计算相关系数和互信息
        extract_function_weights: 是否提取函数的具体参数权重

    Returns:
        dict: 包含该配置的所有结果
    """

    # 设置GPU环境变量（关键步骤）
    os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
    print(f"工作进程开始处理配置 {config_key}，使用GPU {gpu_id}")

    # 重新初始化CUDA上下文（如果使用CUDA）
    if device == 'cuda' and torch.cuda.is_available():
        try:
            # 清空CUDA缓存
            torch.cuda.empty_cache()
            # 在设置CUDA_VISIBLE_DEVICES后，可见的GPU编号从0开始
            torch.cuda.set_device(0)
            print(f"配置 {config_key}: CUDA设备已设置为GPU {gpu_id}")
        except RuntimeError as e:
            print(f"配置 {config_key}: CUDA初始化失败: {e}")
            print(f"配置 {config_key}: 回退到CPU模式")
            device = 'cpu'

    config_name = config_info['name']
    custom_functions = config_info['config']

    print(f"\n{'='*30}\n配置: {config_name} ({config_key})\n{'='*30}")
    print("自定义函数配置:")
    for node, node_config in custom_functions.items():
        print(f"  节点 {node}: {node_config}")

    print(f"开始生成数据集，配置: {config_name}")

    # 生成数据集
    datasets = generate_datasets(
        num_dataset=n,
        h_config=h_config,
        perturbation_type='counterfactual',
        perturbation_node_type=intervention_node_type,
        perturbation_value_method=intervention_value_method,
        custom_dag_type=custom_dag_type,
        custom_functions=custom_functions,
        custom_dag_size=custom_dag_size,  
        node_unobserved=False,
        seed=42,
        allow_skip=True,
        avg_in_degree=avg_in_degree  
    )

    # 初始化结果存储
    config_results = []
    config_snr_results = []
    config_importance_results = []
    config_scm_objects = {}
    config_correlation_results = []  # 存储相关系数结果
    config_mutual_info_results = []  # 存储互信息结果

    # 串行处理该配置下的所有数据集
    for idx, dataset in enumerate(datasets):
        dataset_name = dataset[0]
        xs_original, ys_original = dataset[1].clone().cpu(), dataset[2].clone().cpu()
        xs_intervention, ys_intervention = dataset[3].clone().cpu(), dataset[4].clone().cpu()
        scm = dataset[5]

        # 存储SCM对象
        config_scm_objects[idx] = scm

        print(f"配置 {config_name}: 处理数据集 {idx+1}/{len(datasets)} ({dataset_name})")

        # 收集当前数据集的模型结果
        current_dataset_results = []

        # 获取节点名（用于特征重要性计算）
        feature_names = scm.selected_features if hasattr(scm, 'selected_features') else [f'X{i}' for i in range(xs_original.shape[1])]

        # 转numpy
        xs_original_np = xs_original.numpy()
        ys_original_np = ys_original.numpy()
        xs_intervention_np = xs_intervention.numpy()
        ys_intervention_np = ys_intervention.numpy()

        # 绘制DAG图（使用共享的drawn_dag_signatures避免重复绘制）
        if image_dir is not None and shared_drawn_dag_signatures is not None:
            # 使用共享的drawn_dag_signatures检查是否需要绘制DAG图
            if should_draw_dag_shared(scm, shared_drawn_dag_signatures):
                dag_filename = f'dag_dataset_{idx}_{config_key}.png'
                dag_path = os.path.join(image_dir, dag_filename)

                try:
                    # 保存DAG图
                    intervention_or_perturb_nodes = getattr(scm, 'intervention_nodes', None)
                    if intervention_or_perturb_nodes is None:
                        intervention_or_perturb_nodes = getattr(scm, 'perturbation_nodes', None)

                    draw_graph(
                        scm.dag,
                        dag_path,
                        target_node=getattr(scm, 'selected_target', None),
                        intervention_nodes=intervention_or_perturb_nodes,
                        unobserved_nodes=getattr(scm, 'unobserved_nodes', None),
                        selected_features=getattr(scm, 'selected_features', None),
                        assignment=getattr(scm, 'assignment', None),
                        scm=scm,
                        model_results=None  # 暂时不传递模型结果，避免并行冲突
                    )
                    print(f"配置 {config_name}, 数据集 {idx+1}: 已绘制DAG图 -> {dag_path}")
                except Exception as e:
                    print(f"配置 {config_name}, 数据集 {idx+1}: 绘制DAG图时出错: {str(e)}")
            else:
                print(f"配置 {config_name}, 数据集 {idx+1}: 跳过DAG图绘制（相同结构已存在）")
        else:
            print(f"配置 {config_name}, 数据集 {idx+1}: 跳过DAG图绘制（未提供image_dir或shared_drawn_dag_signatures）")

        # SNR报告与均值提取
        snr_mean = np.nan
        if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
            actual_snr = scm.snr_validation_results.get('actual_snr', {})
            if actual_snr:
                # 过滤掉None值，只计算有效的SNR值
                valid_snr_values = [v for v in actual_snr.values() if v is not None and not np.isnan(v)]
                if valid_snr_values:
                    snr_mean = np.mean(valid_snr_values)
                else:
                    snr_mean = np.nan

        config_snr_results.append({
            'dataset': dataset_name,
            'use_snr': config_key,  # 使用config_key代替use_snr
            'snr_mean': snr_mean
        })

        # 计算训练/测试分割位置
        train_test_split_ratio = h_config.get('train_test_split_ratio', 0.5)
        eval_position = int(xs_original_np.shape[0] * train_test_split_ratio)

        # 准备训练和测试数据
        train_xs, train_ys = xs_original_np[:eval_position], ys_original_np[:eval_position]
        test_xs_original, test_ys_original = xs_original_np[eval_position:], ys_original_np[eval_position:]
        test_xs_intervention, test_ys_intervention = xs_intervention_np[eval_position:], ys_intervention_np[eval_position:]

        # 计算相关系数和互信息（在三种数据集上分别计算）
        if calculate_correlation_mi:
            print(f"配置 {config_name}, 数据集 {idx+1}: 计算相关系数和互信息...")

            # 获取目标节点名称
            target_node = getattr(scm, 'selected_target', None)
            if target_node is None and feature_names:
                # 如果没有明确的目标节点，假设最后一个特征是目标
                target_node = feature_names[-1]

            # 准备三种数据集（train、test、intervention）
            train_data = np.hstack([train_xs, train_ys.reshape(-1, 1)])
            test_data = np.hstack([test_xs_original, test_ys_original.reshape(-1, 1)])
            intervention_data = np.hstack([test_xs_intervention, test_ys_intervention.reshape(-1, 1)])

            # 完整的特征名称（包括目标变量）
            all_feature_names = feature_names + [target_node] if target_node not in feature_names else feature_names

            # 在三种数据集上分别计算相关系数和互信息矩阵
            dataset_correlations = {}
            dataset_mutual_infos = {}

            for split_name, data in [('train', train_data), ('test', test_data), ('intervention', intervention_data)]:
                print(f"配置 {config_name}, 数据集 {idx+1}: 计算{split_name}数据集的相关系数和互信息（样本数: {data.shape[0]}）")
                corr_mi_result = calculate_pairwise_correlations_and_mi(data, all_feature_names)
                dataset_correlations[split_name] = corr_mi_result['correlation_matrix']
                dataset_mutual_infos[split_name] = corr_mi_result['mutual_info_matrix']



            # 存储该数据集的相关系数和互信息结果（三种数据集分别保存）
            config_correlation_results.append({
                'dataset': dataset_name,
                'config_key': config_key,
                'correlation_matrices': dataset_correlations,  # 三种数据集的相关系数矩阵 {train/test/intervention: matrix}
                'target_node': target_node,  # 目标节点名称
                'feature_names': all_feature_names  # 所有特征名称
            })

            config_mutual_info_results.append({
                'dataset': dataset_name,
                'config_key': config_key,
                'mutual_info_matrices': dataset_mutual_infos,  # 三种数据集的互信息矩阵 {train/test/intervention: matrix}
                'target_node': target_node,  # 目标节点名称
                'feature_names': all_feature_names  # 所有特征名称
            })
        else:
            print(f"配置 {config_name}, 数据集 {idx+1}: 跳过相关系数和互信息计算（calculate_correlation_mi=False）")

        # 串行处理该数据集的所有模型类型
        for model_type in model_types:
            print(f"配置 {config_name}, 数据集 {idx+1}: 训练 {model_type.upper()} 模型...")

            try:
                # 训练模型
                model, metrics_train, metrics_test, scaler = run_model_and_eval(
                    train_xs, train_ys, test_xs_original, test_ys_original,
                    model_type=model_type, device=device
                )

                # 在干预数据上评估
                # 对于需要归一化的模型（OLS, Lasso），先对干预数据进行归一化
                if scaler is not None:
                    test_xs_intervention_scaled = scaler.transform(test_xs_intervention)
                    pred_intv = model.predict(test_xs_intervention_scaled)
                else:
                    pred_intv = model.predict(test_xs_intervention)

                r2_intv = r2_score(test_ys_intervention, pred_intv)
                rmse_intv = np.sqrt(mean_squared_error(test_ys_intervention, pred_intv))
                mape_intv = np.mean(np.abs((test_ys_intervention - pred_intv) / (test_ys_intervention + 1e-8))) * 100

                # 存储结果
                result_dict = {
                    'dataset': dataset_name,
                    'model_type': model_type,
                    'use_snr': config_key,  # 使用config_key代替use_snr
                    'r2_train': metrics_train['r2'],
                    'r2_test': metrics_test['r2'],
                    'r2_intv': r2_intv,
                    'rmse_train': metrics_train['rmse'],
                    'rmse_test': metrics_test['rmse'],
                    'rmse_intv': rmse_intv,
                    'mape_train': metrics_train['mape'],
                    'mape_test': metrics_test['mape'],
                    'mape_intv': mape_intv
                }
                config_results.append(result_dict)
                current_dataset_results.append(result_dict)

                # 计算特征重要性
                print(f"配置 {config_name}, 数据集 {idx+1}: 计算 {model_type.upper()} 特征重要性...")
                # 对于需要归一化的模型（OLS, Lasso），使用归一化后的测试数据
                if scaler is not None:
                    test_xs_original_scaled = scaler.transform(test_xs_original)
                    importance = calculate_feature_importance(
                        model, test_xs_original_scaled, test_ys_original, feature_names, model_type=model_type
                    )
                else:
                    importance = calculate_feature_importance(
                        model, test_xs_original, test_ys_original, feature_names, model_type=model_type
                    )

                # 存储特征重要性结果
                config_importance_results.append({
                    'dataset': dataset_name,
                    'model_type': model_type,
                    'use_snr': config_key,  # 使用config_key代替use_snr
                    'importance': importance
                })

                print(f"配置 {config_name}, 数据集 {idx+1}, {model_type.upper()} 训练完成 - R2 Train: {metrics_train['r2']:.3f}, Test: {metrics_test['r2']:.3f}, Intv: {r2_intv:.3f}")

            except Exception as e:
                print(f"配置 {config_name}, 数据集 {idx+1}: 训练 {model_type.upper()} 时出错: {str(e)}")
                traceback.print_exc()

    print(f"配置 {config_name} 处理完成，共处理 {len(datasets)} 个数据集")

    # 返回该配置的所有结果（不包含不可序列化的SCM对象）
    result = {
        'config_key': config_key,
        'config_name': config_name,
        'results': config_results,
        'snr_results': config_snr_results,
        'importance_results': config_importance_results,
        'scm_objects_info': extract_serializable_scm_info(config_scm_objects, extract_function_weights)
    }

    # 只有在计算相关系数和互信息时才添加这些结果
    if calculate_correlation_mi:
        result['correlation_results'] = config_correlation_results  # 相关系数结果
        result['mutual_info_results'] = config_mutual_info_results  # 互信息结果
    else:
        result['correlation_results'] = []  # 空列表
        result['mutual_info_results'] = []  # 空列表

    return result

# ========== 主流程 ===========
def main():

    # 配置参数
    h_config = {
        'device': 'cpu',
        
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        
        'root_distribution': 'uniform',
        'min_root': 0.0,
        'max_root': 1.0,
        'max_range': 0.5,
        'sample_cause_ranges': False,
        'sample_std': False,
        
        'min_num_samples': 1000,
        'max_num_samples': 1000,
        'train_test_split_ratio': 0.7,
        'task': 'regression',
        
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        

        'use_monte_carlo_precompute': False
    }

    h_config.update({
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'sample_cause_ranges': False
    })

    # 设置DAG类型
    custom_dag_type = 'random_SF'  # 可以修改为其他类型进行测试
    custom_dag_size = 'small'
    avg_in_degree = (2.2, 2.7)  # 随机图的平均入度范围，可以修改为其他值进行测试


    intervention_node_type = 'all_non_family' # all_non_family
    intervention_value_method = 'sample'
    n = 2 # 每种方式生成数据集数量

    # 文件保存控制参数
    save_json_file = True  # 是否保存JSON文件的超参数
    calculate_correlation_mi = True # 是否计算相关系数和互信息的超参数

    # 函数参数提取控制参数
    extract_function_weights = True  # 是否提取函数的具体参数权重（如神经网络的权重、线性函数的系数等）


    # R2过滤和有效数据集分析参数
    r2_threshold = 0.5  # R2阈值，只有大于此值的数据集才被认为是有效数据集
    annotate_all_datasets = True  # 是否标注所有数据集上的结果（均值±标准差）
    annotate_effective_datasets = True  # 是否标注有效数据集上的结果（均值±标准差）
    
    # ========== 模型启用配置 ==========
    # 可以通过修改下面的True/False来控制哪些模型要运行
    # 设置为False的模型将被跳过，不会进行训练和评估
    model_config = {
        'ols': True,        # 普通最小二乘法 - 基础线性模型
        'lasso': False,     # LASSO回归 - 暂时禁用（设置为True可启用）
        'catboost': False,   # CatBoost - 梯度提升模型
        'xgboost': True,    # XGBoost - 梯度提升模型
        'lightgbm': True,  # LightGBM - 暂时禁用（设置为True可启用）
        'tabpfn_default': True,  # TabPFN默认模型 - 深度学习模型
        'tabpfn_mse': True,      # TabPFN MSE变体 - 深度学习模型
        'tabpfn_muzero': True    # TabPFN Muzero变体 - 深度学习模型
    }
    # ==========================================
    
    
    # TabPFN设备配置
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 并行处理配置
    num_workers = min(multiprocessing.cpu_count(), 8)  # 限制最大进程数
    
    
    # 动态获取可用GPU列表
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_ids = list(range(gpu_count))  # [0, 1, 2, ...] 根据实际GPU数量
        print(f"检测到 {gpu_count} 个可用GPU: {gpu_ids}")
    else:
        gpu_ids = [0]  # CPU模式，使用虚拟GPU ID 0
        print("未检测到CUDA，将使用CPU模式")

    print(f"并行处理配置: {num_workers} 个工作进程, GPU分配策略: {gpu_ids}")

    # 存储所有结果
    results = []
    snr_results = []
    importance_results = []  # 新增：存储特征重要性结果
    correlation_results = []  # 新增：存储相关系数结果
    mutual_info_results = []  # 新增：存储互信息结果
    scm_objects = {}  # {dataset_idx: {config_key: scm}}

    

    # 确定可用的模型类型 - 按照固定顺序：ols, lasso, catboost, xgboost, lightgbm, tabpfn_default, tabpfn_mse, tabpfn_muzero
    model_types = []

    # 按照固定顺序添加启用的模型
    if model_config['ols']:
        model_types.append('ols')
    if model_config['lasso']:
        model_types.append('lasso')
    if model_config['catboost'] and CATBOOST_AVAILABLE:
        model_types.append('catboost')
    if model_config['xgboost']:
        model_types.append('xgboost')
    if model_config['lightgbm'] and LIGHTGBM_AVAILABLE:
        model_types.append('lightgbm')
    if model_config['tabpfn_default'] and TABPFN_AVAILABLE:
        model_types.append('tabpfn_default')
    if model_config['tabpfn_mse'] and TABPFN_MSE_AVAILABLE:
        model_types.append('tabpfn_mse')
    if model_config['tabpfn_muzero'] and TABPFN_MUZERO_AVAILABLE:
        model_types.append('tabpfn_muzero')

    

    # 初始化已绘制DAG签名集合（用于避免重复绘制）
    drawn_dag_signatures = set()

    print(f"模型配置: {model_config}")
    print(f"启用的模型类型: {model_types}")

    # 检查是否有不可用的模型被启用
    if model_config['catboost'] and not CATBOOST_AVAILABLE:
        print("⚠️  CatBoost已启用但不可用，请安装: pip install catboost")
    if model_config['lightgbm'] and not LIGHTGBM_AVAILABLE:
        print("⚠️  LightGBM已启用但不可用，请安装: pip install lightgbm")
    if model_config['tabpfn_default'] and not TABPFN_AVAILABLE:
        print("⚠️  TabPFN默认模型已启用但不可用，请安装: pip install tabpfn")
    if model_config['tabpfn_mse'] and not TABPFN_MSE_AVAILABLE:
        print("⚠️  TabPFN MSE变体已启用但不可用，请检查inference.regressor_mse模块")
    if model_config['tabpfn_muzero'] and not TABPFN_MUZERO_AVAILABLE:
        print("⚠️  TabPFN Muzero变体已启用但不可用，请检查inference.regressor_muzero模块")


    # 创建唯一的输出目录（只在主进程中创建）
    run_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    image_dir = os.path.join('images', run_time)
    os.makedirs(image_dir, exist_ok=True)
    print(f"输出目录: {image_dir}")


    # 创建共享的drawn_dag_signatures字典，用于跨进程避免重复绘制
    # 使用字典模拟集合功能，键为DAG签名，值为True
    with multiprocessing.Manager() as manager:
        shared_drawn_dag_signatures = manager.dict()

        # 初始化配置字典（无论什么DAG类型都需要）
        custom_functions_configs = {}

        print(f"\\n{'='*50}")
        print(f"测试模式: 不同custom_functions配置比较 (DAG类型: {custom_dag_type})")
        print(f"{'='*50}")

        # 生成i*j个SNR配置
        for i in [2]: #
            for j in [ i * _ for _ in [1]]: 
                for k in [ max(i,j) * _ for _ in [1]]: 
                    key = f'parent_snr_{i}_child_snr_{j}_other_snr_{k}'
                    parent_snr = float(i)  # Y的父节点到Y的边的SNR
                    child_snr = float(j) 
                    other_snr = float(k)   # 其他边的SNR

                    custom_functions_configs[key] = {
                        'name': key,
                        'config': {
                            'target': {
                                'type': 'random_neural_network',
                                'hidden_dim': 2,
                                'depth': 3,
                                'activation': 'tanh',
                                'target_snr': parent_snr
                            },
                            'target_child': {
                                'type': 'random_neural_network',
                                'hidden_dim': 2,
                                'depth': 3,
                                'activation': 'tanh',
                                'target_snr': child_snr
                            },
                            'Other_type': {
                                'type': 'random_neural_network',
                                'hidden_dim': 2,
                                'depth': 3,
                                'activation': 'tanh',
                                'target_snr': other_snr
                            }
                        }
                    }

        # 并行处理不同配置
        print(f"开始并行处理 {len(custom_functions_configs)} 个配置...")

        # 准备任务参数
        task_args = []
        for task_index, (config_key, config_info) in enumerate(custom_functions_configs.items()):
            # 使用轮询策略分配GPU
            assigned_gpu = gpu_ids[task_index % len(gpu_ids)]

            task_args.append((
                config_key, config_info, h_config, n, intervention_node_type,
                intervention_value_method, custom_dag_type, model_types,
                device, assigned_gpu, custom_dag_size, image_dir, shared_drawn_dag_signatures, avg_in_degree, calculate_correlation_mi, extract_function_weights
            ))

        # 使用进程池并行执行
        with concurrent.futures.ProcessPoolExecutor(max_workers=num_workers) as executor:
            # 提交所有任务
            future_to_config = {
                executor.submit(process_single_configuration, *args): args[0]
                for args in task_args
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_config):
                config_key = future_to_config[future]
                try:
                    config_result = future.result()

                    # 合并结果
                    results.extend(config_result['results'])
                    snr_results.extend(config_result['snr_results'])
                    importance_results.extend(config_result['importance_results'])
                    correlation_results.extend(config_result['correlation_results'])
                    mutual_info_results.extend(config_result['mutual_info_results'])

                    # 合并SCM对象信息（现在是序列化的信息而不是完整对象）
                    for dataset_idx, scm_info in config_result['scm_objects_info'].items():
                        if dataset_idx not in scm_objects:
                            scm_objects[dataset_idx] = {}
                        scm_objects[dataset_idx][config_key] = scm_info

                    print(f"配置 {config_result['config_name']} 处理完成")

                except Exception as exc:
                    print(f"配置 {config_key} 处理时发生异常: {exc}")
                    traceback.print_exc()

        print(f"所有配置并行处理完成！")


        # 生成可视化图表
        print("\\n生成可视化图表...")


        # 计算有效数据集（只计算一次）
        print("\\n计算有效数据集...")
        _, effective_datasets = calculate_effective_datasets(results, r2_threshold)
        print(f"计算得到有效数据集: {len(effective_datasets)} 个配置")

        # 生成自适应可视化
        print("\\n生成自适应可视化...")

        # R²性能对比图
        r2_plotter = R2Plotter()
        r2_plotter.plot_r2_comparison(results, image_dir,
                          r2_threshold, annotate_all_datasets, annotate_effective_datasets,
                          effective_datasets=effective_datasets)

        # Permutation importance箱线图对比
        importance_plotter = ImportancePlotter()
        importance_plotter.plot_permutation_importance_comparison(
            importance_results,
            image_dir,
            scm_objects=scm_objects,
            custom_functions_configs=custom_functions_configs,
            results=results,
            r2_threshold=r2_threshold,
            annotate_all_datasets=annotate_all_datasets,
            annotate_effective_datasets=annotate_effective_datasets,
            effective_datasets=effective_datasets
        )



        print("\\n处理综合数据并保存文件...")

        # 保存json文件
        comprehensive_result = process_comprehensive_data(
            scm_objects, results, importance_results,
            image_dir, custom_functions_configs,
            save_json=save_json_file,  # 使用超参数控制JSON保存
            correlation_results=correlation_results,  # 传递相关系数结果
            mutual_info_results=mutual_info_results   # 传递互信息结果
        )

        # 生成汇总CSV文件
        print("\\n生成汇总CSV文件...")
        if comprehensive_result and 'structured_data' in comprehensive_result:
            # 传入custom_dag_type参数以判断是否跳过节点级别信息
            # 传入annotate参数控制保存哪些数据集的结果
            save_summary_csv(image_dir, comprehensive_result['structured_data'],
                                         custom_dag_type=custom_dag_type,
                                         results=results, r2_threshold=r2_threshold,
                                         annotate_all_datasets=annotate_all_datasets,
                                         annotate_effective_datasets=annotate_effective_datasets,
                                         effective_datasets=effective_datasets)
        else:
            print("警告：没有内存数据可用于生成汇总CSV文件")

    
        print(f"\\n所有图表已保存到: {image_dir}")



if __name__ == '__main__':
    # Linux系统上设置multiprocessing启动方法为spawn，解决CUDA在fork子进程中无法重新初始化的问题
    multiprocessing.set_start_method('spawn', force=True)
    main()
