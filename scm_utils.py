import networkx as nx
import random
import numpy as np
import scipy.stats as stats
import matplotlib

matplotlib.use('Agg')
import matplotlib.pyplot as plt

import torch
import copy
import torch.nn as nn
import math
import os

# Util functions
# 定义截断正态分布采样器函数，参数 mu 为均值，sigma 为标准差
trunc_norm_sampler_f = lambda mu, sigma, seed=None: lambda: \
    stats.truncnorm((0 - mu) / sigma, (1000000 - mu) / sigma, loc=mu, scale=sigma).rvs(1, random_state=seed)[0]
# 定义 Beta 分布采样器函数，参数 a 和 b 为 Beta 分布的形状参数
beta_sampler_f = lambda a, b, seed=None: lambda: \
    np.random.RandomState(seed).beta(a, b) if seed is not None else np.random.beta(a, b)
# 定义 Gamma 分布采样器函数，参数 a 和 b 为 Gamma 分布的形状参数和尺度参数
gamma_sampler_f = lambda a, b, seed=None: lambda: \
    np.random.RandomState(seed).gamma(a, b) if seed is not None else np.random.gamma(a, b)
# 定义均匀分布采样器函数，参数 a 和 b 为均匀分布的下界和上界
uniform_sampler_f = lambda a, b, seed=None: lambda: \
    np.random.RandomState(seed).uniform(a, b) if seed is not None else np.random.uniform(a, b)
# 定义均匀整数分布采样器函数，参数 a 和 b 为均匀分布的下界和上界，结果会四舍五入为整数
uniform_int_sampler_f = lambda a, b, seed=None: lambda: \
    round(np.random.RandomState(seed).uniform(a, b)) if seed is not None else round(np.random.uniform(a, b))


# 定义一个函数，用于采样原因节点的取值范围
def causes_sampler_f(num_causes, min_lb, max_lb, max_len, seed=None):
    # 断言确保 max_lb 大于 min_lb
    assert (max_lb - min_lb > 0)
    # 断言确保 max_lb 加上 max_len 不超过 1
    assert (max_lb + max_len <= 1)

    # 如果提供了种子，创建一个随机状态
    if seed is not None:
        rng = np.random.RandomState(seed)
        # 从 [min_lb, max_lb) 均匀采样 num_causes 个下界值
        lb = rng.uniform(low=min_lb, high=max_lb, size=num_causes)
        # 在下界的基础上，从 [0, max_len) 均匀采样长度，并加到下界上得到上界
        ub = lb + rng.uniform(low=0.0, high=max_len, size=num_causes)
    else:
        # 从 [min_lb, max_lb) 均匀采样 num_causes 个下界值
        lb = np.random.uniform(low=min_lb, high=max_lb, size=num_causes)
        # 在下界的基础上，从 [0, max_len) 均匀采样长度，并加到下界上得到上界
        ub = lb + np.random.uniform(low=0.0, high=max_len, size=num_causes)

    # 返回下界和上界数组
    return lb, ub

# 定义一个类别采样器函数，用于生成类别数量
def class_sampler_f(min_: int, max_: int, seed=None):
    # 定义内部采样函数 s
    def s():
        # 如果提供了种子，使用固定的随机源
        if seed is not None:
            # 创建随机状态
            rng = random.Random(seed)
            # 以 0.5 的概率决定采样方式
            if rng.random() > 0.5:
                # 如果随机数大于 0.5，则从 [min_, max_] 均匀采样一个整数作为类别数量
                return uniform_int_sampler_f(min_, max_, seed)()
            # 否则，返回固定的类别数量 2
            return 2
        else:
            # 不使用种子，以 0.5 的概率决定采样方式
            if random.random() > 0.5:
                # 如果随机数大于 0.5，则从 [min_, max_] 均匀采样一个整数作为类别数量
                return uniform_int_sampler_f(min_, max_)()
            # 否则，返回固定的类别数量 2
            return 2

    # 返回内部采样函数 s
    return s

# 定义一个函数，用于随机化类别标签
def randomize_classes(x, num_classes, seed=None):
    # 创建一个从 0 到 num_classes-1 的类别张量，设备与 x 相同
    classes = torch.arange(0, num_classes, device=x.device)

    # 如果提供了种子，设置随机种子
    if seed is not None:
        # 保存当前状态
        rng_state = torch.get_rng_state()
        if torch.cuda.is_available():
            cuda_rng_state = torch.cuda.get_rng_state()

        # 设置新的随机种子
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)

        # 创建随机排列
        random_classes = torch.randperm(num_classes, device=x.device).type(x.type())

        # 恢复原始状态
        torch.set_rng_state(rng_state)
        if torch.cuda.is_available():
            torch.cuda.set_rng_state(cuda_rng_state)
    else:
        # 不设置种子，直接生成随机排列
        random_classes = torch.randperm(num_classes, device=x.device).type(x.type())

    # 将 x 的最后一维扩展，并与原始类别比较，然后乘以新的随机类别，最后求和得到新的类别标签
    x = ((x.unsqueeze(-1) == classes) * random_classes).sum(-1)
    # 返回随机化后的类别标签
    return x

def draw_graph(G, path, target_node=None, intervention_nodes=None, unobserved_nodes=None, selected_features=None, assignment=None, scm=None, model_results=None):
    """
    绘制有向图，并用特殊颜色标记目标节点、干预/扰动节点和被选择的特征节点
    参数:
        G: NetworkX DiGraph 对象
        path: 保存图像的路径
        target_node: 目标节点，将用红色标记
        intervention_nodes: 干预/扰动节点或节点集合，将用蓝色标记
        unobserved_nodes: 不可观测的节点或节点集合，将添加阴影效果和虚线边框
        selected_features: 被选择作为特征的节点集合，将用绿色标记
        scm: SCM对象，用于获取SNR验证结果
        model_results: 模型评估结果列表，包含R2、RMSE、MAPE等指标
    """
    # 创建一个图形窗口，设置大小为 8x6 英寸
    plt.figure(figsize=(8, 6))

    # 使用 graphviz 的 "dot" 布局算法计算节点位置
    pos = nx.nx_agraph.graphviz_layout(G, prog="dot")

    # 将参数标准化为集合
    if intervention_nodes is not None:
        if isinstance(intervention_nodes, (list, set, tuple)):
            intervention_nodes_set = set(intervention_nodes)
        else:
            intervention_nodes_set = {intervention_nodes}
    else:
        intervention_nodes_set = set()

    if unobserved_nodes is not None:
        if isinstance(unobserved_nodes, (list, set, tuple)):
            unobserved_nodes_set = set(unobserved_nodes)
        else:
            unobserved_nodes_set = {unobserved_nodes}
    else:
        unobserved_nodes_set = set()

    if selected_features is not None:
        if isinstance(selected_features, (list, set, tuple)):
            selected_features_set = set(selected_features)
        else:
            selected_features_set = {selected_features}
    else:
        selected_features_set = set()

    # 准备节点颜色列表
    node_colors = []
    for node in G.nodes():
        if node == target_node:
            node_colors.append('#dbc4ce')
        elif node in intervention_nodes_set:
            node_colors.append('#f3e6bd')
        elif node in selected_features_set:
            node_colors.append('#a7cbd3')
        else:
            node_colors.append('#a9afcb')

    # 根据节点数量自适应调整字体大小和节点大小
    num_nodes = len(G.nodes())
    if num_nodes <= 30:
        font_size = 10
        node_size = 300
    elif num_nodes <= 50:
        font_size = 8
        node_size = 200
    elif num_nodes <= 100:
        font_size = 5
        node_size = 150
    else:
        font_size = 3
        node_size = 100

    # 绘制基本图形
    nx.draw(G, pos, with_labels=True, node_size=node_size,
            node_color=node_colors, edge_color='gray', arrows=True,
            font_color='white', font_weight='bold', font_size=font_size)

    # 为不可观测的节点添加阴影效果和虚线边框
    for node in unobserved_nodes_set:
        if node in G.nodes():
            # 绘制阴影效果（略大一点的灰色节点在底层）
            plt.scatter(pos[node][0], pos[node][1], s=600, color='gray', alpha=0.5)
            # 绘制虚线边框，表示不可观测
            circle = plt.Circle((pos[node][0], pos[node][1]), radius=25, fill=False, linestyle='dashed', edgecolor='black', linewidth=2)
            plt.gca().add_patch(circle)
    # 集中打印每对(parent, child)的f/g/init_std/noise_std信息
    if assignment is not None:
        info_lines = []

        # 获取SNR验证结果（如果有）
        snr_validation_results = None
        if scm is not None and hasattr(scm, 'snr_validation_results'):
            snr_validation_results = scm.snr_validation_results

        for node in G.nodes():
            parents = list(G.predecessors(node))
            if not parents:
                continue
            node_info = f"parents: {parents} -> child: {node}"
            if node in assignment:
                model_info = assignment[node]['assignment'].get_model_info()
                f_layers = model_info.get('f_layers', [])
                f_layer_strs = []
                for idx, layer in enumerate(f_layers):
                    if layer['type'] == 'Linear':
                        w = layer['weight']
                        b = layer['bias']
                        w_str = str(w[:5]) if len(w) > 5 else str(w)
                        b_str = str(b[:5]) if len(b) > 5 else str(b)
                        f_layer_strs.append(f"Linear\nw={w_str}\nb={b_str}")
                    else:
                        f_layer_strs.append(layer['type'])
                f_layers_info = '\n---\n'.join(f_layer_strs)
                g_str = model_info.get('g_function', '')
                init_std = model_info.get('init_std', 'N/A')
                noise_std = model_info.get('noise_std', 'N/A')
                node_info += f"\n  f函数:\n{f_layers_info}"
                node_info += f"\n  g函数: {g_str}"
                node_info += f"\n  init_std: {init_std}"
                node_info += f"\n  noise_std: {noise_std}"

                # 添加SNR验证结果信息
                if snr_validation_results is not None:
                    signal_var = snr_validation_results.get('signal_var', {}).get(node, 'N/A')
                    actual_noise_var = snr_validation_results.get('actual_noise_var', {}).get(node, 'N/A')
                    actual_snr = snr_validation_results.get('actual_snr', {}).get(node, 'N/A')
                    target_snr = snr_validation_results.get('target_snr', {}).get(node, 'N/A')

                    # 计算标准差
                    signal_std = 'N/A'
                    actual_noise_std = 'N/A'
                    if signal_var != 'N/A' and isinstance(signal_var, (int, float)):
                        signal_std = f"{signal_var**0.5:.6f}"
                        signal_var = f"{signal_var:.6f}"
                    if actual_noise_var != 'N/A' and isinstance(actual_noise_var, (int, float)):
                        actual_noise_std = f"{actual_noise_var**0.5:.6f}"
                        actual_noise_var = f"{actual_noise_var:.6f}"

                    node_info += f"\n  === SNR验证结果 ==="
                    node_info += f"\n  实际信号方差: {signal_var}"
                    node_info += f"\n  实际信号标准差: {signal_std}"
                    node_info += f"\n  实际噪声方差: {actual_noise_var}"
                    node_info += f"\n  实际噪声标准差: {actual_noise_std}"
                    node_info += f"\n  实际SNR: {actual_snr}"
                    node_info += f"\n  目标SNR: {target_snr}"

            info_lines.append(node_info)

        # 添加整体SNR验证摘要
        if snr_validation_results is not None and 'summary' in snr_validation_results:
            summary = snr_validation_results['summary']
            summary_info = "\n=== 整体SNR验证摘要 ==="
            summary_info += f"\n平均绝对误差: {summary.get('mean_absolute_error', 'N/A')}"
            summary_info += f"\n最大绝对误差: {summary.get('max_absolute_error', 'N/A')}"
            summary_info += f"\n平均相对误差: {summary.get('mean_relative_error', 'N/A')}"
            summary_info += f"\n最大相对误差: {summary.get('max_relative_error', 'N/A')}"
            summary_info += f"\n验证节点数: {summary.get('num_nodes', 'N/A')}"
            info_lines.append(summary_info)

        # 添加模型评估结果
        if model_results is not None and len(model_results) > 0:
            model_info = "\n=== 模型评估结果 ==="

            # 按模型类型分组
            models_by_type = {}
            for result in model_results:
                model_type = result.get('model_type', 'unknown')
                if model_type not in models_by_type:
                    models_by_type[model_type] = []
                models_by_type[model_type].append(result)

            for model_type, results in models_by_type.items():
                model_info += f"\n\n--- {model_type.upper()} 模型 ---"
                for result in results:
                    dataset_name = result.get('dataset', 'unknown')
                    config_name = result.get('use_snr', 'unknown')
                    model_info += f"\n数据集: {dataset_name}, 配置: {config_name}"

                    # R2指标
                    model_info += f"\n  R² - Train: {result.get('r2_train', 'N/A'):.4f}, Test: {result.get('r2_test', 'N/A'):.4f}, Intervention: {result.get('r2_intv', 'N/A'):.4f}"

                    # RMSE指标
                    model_info += f"\n  RMSE - Train: {result.get('rmse_train', 'N/A'):.4f}, Test: {result.get('rmse_test', 'N/A'):.4f}, Intervention: {result.get('rmse_intv', 'N/A'):.4f}"

                    # MAPE指标
                    model_info += f"\n  MAPE - Train: {result.get('mape_train', 'N/A'):.2f}%, Test: {result.get('mape_test', 'N/A'):.2f}%, Intervention: {result.get('mape_intv', 'N/A'):.2f}%"

            info_lines.append(model_info)


        # 保存参数信息到txt
        txt_path = os.path.splitext(path)[0] + '.txt'
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write('\n\n'.join(info_lines))
    # 保存
    plt.savefig(path, dpi=300, bbox_inches='tight')
    plt.close()
    
## Find specific nodes in a graph
# 定义一个函数，获取图中一个节点的父节点集合
def get_parents(graph, node):
    # 返回节点的所有直接前驱（父节点）的集合
    return set(graph.predecessors(node))

# 定义一个函数，获取图中一个节点的子节点集合
def get_children(graph, node):
    # 返回节点的所有直接后继（子节点）的集合
    return set(graph.successors(node))

# 定义一个函数，获取图中一个节点的配偶节点集合（共同孩子的其他父母）
def get_spouses(graph, node):
    # 初始化配偶节点集合
    spouses = set()
    # 获取父节点和子节点，用于排除
    parents = set(graph.predecessors(node))
    children = set(graph.successors(node))

    # 遍历节点的每个子节点
    for child in graph.successors(node):
        # 遍历子节点的每个父节点
        for parent in graph.predecessors(child):
            # 如果父节点不是节点本身，且不是节点的父节点或子节点，则为配偶节点
            if parent != node and parent not in parents and parent not in children:
                # 将配偶节点添加到集合中
                spouses.add(parent)
    # 返回配偶节点集合
    return spouses

# 定义一个函数，获取图中一个节点的所有后代节点
def get_all_descendants(graph, node):
    # 初始化已访问节点集合
    visited = set()
    # 初始化栈，并将起始节点加入栈中
    stack = [node]

    # 初始化后代节点集合
    descendants = set()

    # 当栈不为空时循环
    while stack:
        # 弹出栈顶节点作为当前节点
        current = stack.pop()
        # 遍历当前节点的直接后继（子节点）
        for child in graph.successors(current):
            # 如果子节点未被访问过
            if child not in visited:
                # 将子节点标记为已访问
                visited.add(child)
                # 将子节点添加到后代节点集合中
                descendants.add(child)
                # 将子节点压入栈中，继续查找其后代
                stack.append(child)
    # 返回所有后代节点集合
    return descendants

# 定义一个函数，获取图中一个节点的祖父节点集合
def get_grandparents(graph, node):
    # 初始化祖父节点集合
    grandparents = set()
    # 获取父节点、子节点、配偶节点和兄弟节点，用于排除
    parents = set(graph.predecessors(node))
    children = set(graph.successors(node))
    spouses = set()
    siblings = set()

    # 计算配偶节点
    for child in graph.successors(node):
        for parent in graph.predecessors(child):
            if parent != node and parent not in parents and parent not in children:
                spouses.add(parent)

    # 计算兄弟节点
    for parent in graph.predecessors(node):
        for child in graph.successors(parent):
            if child != node and child not in parents and child not in children and child not in spouses:
                siblings.add(child)

    # 遍历节点的每个父节点
    for parent in graph.predecessors(node):
        # 遍历父节点的每个父节点（即祖父节点）
        for grandparent in graph.predecessors(parent):
            # 如果祖父节点不是已分类的其他类型节点，则添加到集合中
            if (grandparent not in parents and grandparent not in children and
                grandparent not in spouses and grandparent not in siblings):
                grandparents.add(grandparent)
    # 返回祖父节点集合
    return grandparents

# 定义一个函数，获取图中一个节点的所有祖先节点
def get_all_antecedents(graph, node):
    # 初始化已访问节点集合
    visited = set()
    # 初始化栈，并将起始节点加入栈中
    stack = [node]

    # 初始化祖先节点集合
    antecedents = set()

    # 当栈不为空时循环
    while stack:
        # 弹出栈顶节点作为当前节点
        current = stack.pop()
        # 遍历当前节点的直接前驱（父节点）
        for parent in graph.predecessors(current):
            # 如果父节点未被访问过
            if parent not in visited:
                # 将父节点标记为已访问
                visited.add(parent)
                # 将父节点添加到祖先节点集合中
                antecedents.add(parent)
                # 将父节点压入栈中，继续查找其祖先
                stack.append(parent)
    # 返回所有祖先节点集合
    return antecedents

# 定义一个函数，获取图中一个节点的兄弟节点集合
def get_siblings(graph, node):
    # 初始化兄弟节点集合
    siblings = set()
    # 获取父节点、子节点和配偶节点，用于排除
    parents = set(graph.predecessors(node))
    children = set(graph.successors(node))
    spouses = set()
    for child in graph.successors(node):
        for parent in graph.predecessors(child):
            if parent != node and parent not in parents and parent not in children:
                spouses.add(parent)

    # 遍历节点的每个父节点
    for parent in graph.predecessors(node):
        # 遍历父节点的每个子节点
        for child in graph.successors(parent):
            # 如果子节点不是节点本身，且不是已分类的父节点、子节点、配偶节点，则为兄弟节点
            if child != node and child not in parents and child not in children and child not in spouses:
                # 将兄弟节点添加到集合中
                siblings.add(child)
    # 返回兄弟节点集合
    return siblings


def get_exclusive_node_relationships(graph, target_node):
    """
    按照优先级获取目标节点的各种关系节点，确保节点类型互斥
    优先级：父亲节点 > 孩子节点 > 配偶节点 > 祖父节点 > 兄弟节点 > 其他节点

    参数:
        graph: NetworkX DiGraph对象
        target_node: 目标节点

    返回:
        dict: 包含各种关系节点的字典
        {
            'parents': set,      # 父节点
            'children': set,     # 子节点
            'spouses': set,      # 配偶节点（排除已分类的父子节点）
            'grandparents': set, # 祖父节点（排除已分类的节点）
            'siblings': set,     # 兄弟节点（排除已分类的节点）
            'others': set        # 其他节点
        }
    """
    all_nodes = set(graph.nodes())
    classified_nodes = {target_node}  # 已分类的节点，包括目标节点本身

    # 1. 父节点（最高优先级）
    parents = set(graph.predecessors(target_node))
    classified_nodes.update(parents)

    # 2. 子节点（第二优先级）
    children = set(graph.successors(target_node))
    classified_nodes.update(children)

    # 3. 配偶节点（第三优先级）- 与目标节点有共同子节点的节点，排除已分类的节点
    spouses = set()
    for child in children:
        for parent in graph.predecessors(child):
            if parent not in classified_nodes:
                spouses.add(parent)
    classified_nodes.update(spouses)

    # 4. 祖父节点（第四优先级）- 父节点的父节点，排除已分类的节点
    grandparents = set()
    for parent in parents:
        for grandparent in graph.predecessors(parent):
            if grandparent not in classified_nodes:
                grandparents.add(grandparent)
    classified_nodes.update(grandparents)

    # 5. 兄弟节点（第五优先级）- 与目标节点有共同父节点的节点，排除已分类的节点
    siblings = set()
    for parent in parents:
        for sibling in graph.successors(parent):
            if sibling not in classified_nodes:
                siblings.add(sibling)
    classified_nodes.update(siblings)

    # 6. 其他节点（最低优先级）- 剩余的所有节点
    others = all_nodes - classified_nodes

    return {
        'parents': parents,
        'children': children,
        'spouses': spouses,
        'grandparents': grandparents,
        'siblings': siblings,
        'others': others
    }

def classify_edges_by_target_relationship(graph, target_node):
    """
    根据目标节点对图中所有边进行分类

    参数:
        graph: NetworkX DiGraph对象
        target_node: 目标节点

    返回:
        dict: 边分类字典，格式为 {edge_type: [edge_list]}
        edge_type包括:
        - 'Yparent_Y': 目标节点的父节点到目标节点的边
        - 'Y_Ychild': 目标节点到其子节点的边
        - 'Yspouse_Ychild': 目标节点的配偶到共同子节点的边
        - 'Yparent_Ysibling': 目标节点与兄弟节点共同的父节点到兄弟节点的边
        - 'Ygrandparent_Yparent': 目标节点的祖父节点到父节点的边
        - 'Other': 其他所有边
    """
    if target_node not in graph.nodes():
        raise ValueError(f"目标节点 {target_node} 不在图中")

    # 获取目标节点的各种关系节点
    parents = get_parents(graph, target_node)
    children = get_children(graph, target_node)
    siblings = get_siblings(graph, target_node)
    spouses = get_spouses(graph, target_node)
    grandparents = get_grandparents(graph, target_node)

    # 初始化边分类字典
    edge_classification = {
        'Yparent_Y': [],
        'Y_Ychild': [],
        'Yspouse_Ychild': [],
        'Yparent_Ysibling': [],
        'Ygrandparent_Yparent': [],
        'Other': []
    }

    # 遍历图中所有边进行分类
    for edge in graph.edges():
        source, edge_target = edge  # 重命名避免与target_node冲突
        edge_classified = False

        # 1. Yparent_Y: 父节点到目标节点的边
        if source in parents and edge_target == target_node:
            edge_classification['Yparent_Y'].append(edge)
            edge_classified = True

        # 2. Y_Ychild: 目标节点到子节点的边
        elif source == target_node and edge_target in children:
            edge_classification['Y_Ychild'].append(edge)
            edge_classified = True

        # 3. Yspouse_Ychild: 配偶节点到共同子节点的边
        elif source in spouses and edge_target in children:
            # 确认这确实是共同子节点（既是目标节点的子节点，也是配偶的子节点）
            if edge_target in graph.successors(target_node) and edge_target in graph.successors(source):
                edge_classification['Yspouse_Ychild'].append(edge)
                edge_classified = True

        # 4. Yparent_Ysibling: 共同父节点到兄弟节点的边
        elif source in parents and edge_target in siblings:
            # 确认source确实是edge_target的父节点
            if edge_target in graph.successors(source):
                edge_classification['Yparent_Ysibling'].append(edge)
                edge_classified = True

        # 5. Ygrandparent_Yparent: 祖父节点到父节点的边
        elif source in grandparents and edge_target in parents:
            # 确认source确实是edge_target的父节点
            if edge_target in graph.successors(source):
                edge_classification['Ygrandparent_Yparent'].append(edge)
                edge_classified = True

        # 6. Other: 其他所有边
        if not edge_classified:
            edge_classification['Other'].append(edge)

    return edge_classification

def classify_nodes_by_target_relationship_type(graph, target_node):
    """
    根据目标节点对图中所有节点进行类型分类

    参数:
        graph: NetworkX DiGraph对象
        target_node: 目标节点

    返回:
        dict: 节点类型分类字典，格式为 {node_type: [node_list]}
        node_type包括:
        - 'target': 目标节点本身
        - 'target_child': 目标节点的子节点列表
        - 'Other_type': 其他所有节点列表（除了目标节点和目标节点的子节点）
    """
    if target_node not in graph.nodes():
        raise ValueError(f"目标节点 {target_node} 不在图中")

    # 获取Y的子节点
    children = get_children(graph, target_node)

    # 获取所有节点
    all_nodes = set(graph.nodes())

    # 分类节点
    node_classification = {
        'target': [target_node],
        'target_child': list(children),
        'Other_type': list(all_nodes - {target_node} - children)
    }

    return node_classification

def detect_node_type_config(custom_functions, target_node):
    """
    检测自定义函数配置是否使用节点类型级配置

    参数:
        custom_functions: 自定义函数配置字典
        target_node: 目标节点

    返回:
        bool: True表示使用节点类型级配置，False表示使用传统节点级配置
    """
    if custom_functions is None:
        return False

    # 节点类型级配置的关键字
    node_type_keys = {'target', 'target_child', 'Other_type'}

    # 检查是否包含节点类型关键字
    config_keys = set(custom_functions.keys())
    has_node_type_keys = bool(config_keys & node_type_keys)

    # 检查是否包含具体节点名称（如果target_node存在的话）
    has_specific_node_keys = target_node in config_keys if target_node else False

    # 如果同时包含节点类型和具体节点名称，优先使用节点类型配置
    if has_node_type_keys:
        return True
    elif has_specific_node_keys:
        return False
    else:
        # 如果都没有，检查是否有边级配置
        edge_type_keys = {'Yparent_Y', 'Y_Ychild', 'Yspouse_Ychild', 'Yparent_Ysibling', 'Ygrandparent_Yparent'}
        has_edge_type_keys = bool(config_keys & edge_type_keys)
        return not has_edge_type_keys  # 如果没有边级配置，默认为节点类型配置

def get_node_type_for_node(node, node_classification):
    """
    获取指定节点的类型

    参数:
        node: 要查询的节点
        node_classification: 节点分类字典

    返回:
        str: 节点类型 ('target', 'target_child', 'Other_type')
    """
    for node_type, nodes in node_classification.items():
        if node in nodes:
            return node_type
    return 'Other_type'  # 默认返回Other_type类型

def get_edge_function_config(edge, edge_classification, custom_functions):
    """
    根据边的分类获取对应的函数配置

    参数:
        edge: 边元组 (source, target)
        edge_classification: 边分类字典
        custom_functions: 自定义函数配置字典

    返回:
        dict: 该边对应的函数配置，如果没有找到则返回None
    """
    if custom_functions is None:
        return None

    # 检查是否是新的边级配置格式
    edge_type_keys = ['Yparent_Y', 'Y_Ychild', 'Yspouse_Ychild', 'Yparent_Ysibling', 'Ygrandparent_Yparent', 'Other']
    is_edge_based_config = any(key in custom_functions for key in edge_type_keys)

    if not is_edge_based_config:
        # 如果是旧的节点级配置，返回目标节点的配置
        source, target = edge
        return custom_functions.get(target, None)

    # 新的边级配置：找到该边属于哪个类型
    for edge_type, edges in edge_classification.items():
        if edge in edges:
            return custom_functions.get(edge_type, None)

    # 如果没有找到分类，使用Other类别的配置
    return custom_functions.get('Other', None)
