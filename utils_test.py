import os
import json
import pandas as pd
import numpy as np
import traceback
import networkx as nx
from datetime import datetime
from collections import OrderedDict
from sklearn.feature_selection import mutual_info_regression
from utils_scm import get_exclusive_node_relationships



class DataExtractor:
    """数据提取器基类，封装通用的数据提取逻辑"""
    
    @staticmethod
    def safe_extract_attr(obj, attr_name, default=None, error_prefix=""):
        """安全提取对象属性，统一错误处理"""
        try:
            if hasattr(obj, attr_name):
                return getattr(obj, attr_name)
            return default
        except Exception as e:
            if error_prefix:
                print(f"{error_prefix}: {e}")
            return f"Error: {str(e)}" if default is None else default
    
    @staticmethod
    def safe_call_method(obj, method_name, default=None, error_prefix=""):
        """安全调用对象方法，统一错误处理"""
        try:
            if hasattr(obj, method_name):
                method = getattr(obj, method_name)
                return method() if callable(method) else method
            return default
        except Exception as e:
            if error_prefix:
                print(f"{error_prefix}: {e}")
            return f"Error: {str(e)}" if default is None else default
    
    @staticmethod
    def convert_to_serializable(value):
        """将值转换为可序列化格式"""
        if hasattr(value, 'detach'):  # torch.Tensor
            return value.detach().cpu().numpy().tolist()
        elif isinstance(value, (list, tuple)):
            return list(value)
        elif isinstance(value, (str, int, float, bool, type(None))):
            return value
        elif hasattr(value, 'tolist'):  # numpy array
            return value.tolist()
        else:
            return str(value)


class DAGManager:
    """DAG管理器，处理DAG相关的操作"""
    
    @staticmethod
    def normalize_node_list(node_list):
        """标准化节点列表为排序的元组"""
        if node_list is None:
            return None
        if isinstance(node_list, (list, set, tuple)):
            return tuple(sorted(node_list))
        else:
            return (node_list,)
    
    @staticmethod
    def create_dag_signature(scm=None, scm_info=None):
        """创建DAG的完整签名"""
        if scm is not None:
            if not hasattr(scm, 'dag'):
                return None
            
            nodes = tuple(sorted(list(scm.dag.nodes())))
            edges = tuple(sorted(list(scm.dag.edges())))
            target_node = DataExtractor.safe_extract_attr(scm, 'selected_target')
            intervention_nodes = (DataExtractor.safe_extract_attr(scm, 'intervention_nodes') or 
                                DataExtractor.safe_extract_attr(scm, 'perturbation_nodes'))
            selected_features = DataExtractor.safe_extract_attr(scm, 'selected_features')
            unobserved_nodes = DataExtractor.safe_extract_attr(scm, 'unobserved_nodes')
            
        elif scm_info is not None:
            if 'dag_nodes' not in scm_info or 'dag_edges' not in scm_info:
                return None
            
            nodes = tuple(sorted(scm_info['dag_nodes']))
            edges = tuple(sorted(scm_info['dag_edges']))
            target_node = scm_info.get('selected_target')
            intervention_nodes = (scm_info.get('intervention_nodes') or 
                                scm_info.get('perturbation_nodes'))
            selected_features = scm_info.get('selected_features')
            unobserved_nodes = scm_info.get('unobserved_nodes')
        else:
            raise ValueError("必须提供scm或scm_info参数之一")
        
        # 标准化所有节点列表
        intervention_nodes = DAGManager.normalize_node_list(intervention_nodes)
        selected_features = DAGManager.normalize_node_list(selected_features)
        unobserved_nodes = DAGManager.normalize_node_list(unobserved_nodes)
        
        return (nodes, edges, target_node, intervention_nodes, selected_features, unobserved_nodes)
    
    @staticmethod
    def build_temp_dag(dag_nodes, dag_edges):
        """构建临时DAG图对象"""
        temp_dag = nx.DiGraph()
        temp_dag.add_nodes_from(dag_nodes)
        temp_dag.add_edges_from(dag_edges)
        return temp_dag
    
    @staticmethod
    def get_topological_order(dag_nodes, dag_edges, all_nodes):
        """获取拓扑排序"""
        temp_dag = DAGManager.build_temp_dag(dag_nodes, dag_edges)
        try:
            return list(nx.topological_sort(temp_dag))
        except nx.NetworkXError:
            return sorted(all_nodes)
    
    @staticmethod
    def should_draw_dag_shared(scm, shared_drawn_dag_signatures):
        """检查是否应该绘制DAG图"""
        dag_signature = DAGManager.create_dag_signature(scm=scm)
        if dag_signature is None:
            return False
        
        signature_key = str(dag_signature)
        if signature_key in shared_drawn_dag_signatures:
            return False
        
        shared_drawn_dag_signatures[signature_key] = True
        return True


class FunctionParameterExtractor:
    """函数参数提取器"""
    
    COMMON_ATTRS = ['coefficients', 'weights', 'bias', 'powers', 'exponents',
                   'hidden_dim', 'depth', 'lengthscale', 'f_magn', 'a_coeffs',
                   'b_coeffs', 'n_terms', 'x_points', 'y_values']
    
    @staticmethod
    def extract_linear_params(f_func, parents, node, func_type):
        """提取线性函数参数"""
        try:
            if not (hasattr(f_func, 'weight') and hasattr(f_func, 'bias')):
                return None, False
            
            print(f"  检测到线性函数，提取权重和偏置...")
            weights = f_func.weight.detach().cpu().numpy().flatten().tolist()
            bias = f_func.bias.detach().cpu().numpy().item() if f_func.bias is not None else 0.0
            
            if len(parents) > 0 and len(weights) >= len(parents):
                used_weights = weights[:len(parents)]
                expression = FunctionParameterExtractor._build_linear_expression(
                    node, parents, used_weights, bias)
                
                generation_mechanism = {
                    'expression': expression,
                    'weights': {parent: weight for parent, weight in zip(parents, used_weights)},
                    'bias': bias,
                    'parameters': {'coefficients': used_weights, 'bias': bias},
                    'function_type': func_type
                }
                
                function_config_update = {
                    'w': [used_weights],
                    'b': [bias],
                    'g_function': 'identity'
                }
                
                return (generation_mechanism, function_config_update), True
            
        except Exception as e:
            print(f"提取线性函数参数时出错 (节点 {node}): {e}")
            return FunctionParameterExtractor._create_error_result(node, func_type, parents, e), False
    
    @staticmethod
    def _build_linear_expression(node, parents, weights, bias):
        """构建线性表达式"""
        terms = []
        for parent, weight in zip(parents, weights):
            if abs(weight) > 1e-6:
                if abs(weight - 1.0) < 1e-6:
                    terms.append(f"{parent}")
                elif abs(weight + 1.0) < 1e-6:
                    terms.append(f"-{parent}")
                else:
                    terms.append(f"{weight:.4f}*{parent}")
        
        if abs(bias) > 1e-6:
            terms.append(f"{bias:.4f}")
        
        expression = " + ".join(terms).replace("+ -", "- ")
        return f"{node} = {expression if expression else '0'}"
    
    @staticmethod
    def extract_neural_network_params(f_func, parents, node, func_type):
        """提取神经网络参数"""
        try:
            if not hasattr(f_func, 'network'):
                return None, False
            
            print(f"  检测到神经网络函数，提取网络结构...")
            network_info = {'input_dim': len(parents), 'layers': []}
            detailed_params = {}
            
            for layer_idx, layer in enumerate(f_func.network):
                if hasattr(layer, 'weight'):
                    weights = layer.weight.detach().cpu().numpy().tolist()
                    bias = layer.bias.detach().cpu().numpy().tolist() if layer.bias is not None else None
                    
                    layer_info = {
                        'type': 'linear',
                        'input_size': layer.weight.shape[1],
                        'output_size': layer.weight.shape[0],
                        'weights_shape': list(layer.weight.shape),
                        'has_bias': layer.bias is not None,
                        'weights': weights,
                        'bias': bias
                    }
                    network_info['layers'].append(layer_info)
                    detailed_params[f'layer_{layer_idx}_weights'] = weights
                    if bias is not None:
                        detailed_params[f'layer_{layer_idx}_bias'] = bias
                        
                elif hasattr(layer, '__class__'):
                    layer_info = {'type': layer.__class__.__name__.lower()}
                    network_info['layers'].append(layer_info)
            
            generation_mechanism = {
                'expression': f"{node} = neural_network({', '.join(parents)})",
                'network_structure': network_info,
                'detailed_parameters': detailed_params,
                'function_type': func_type,
                'parents': parents
            }
            
            function_config_update = {
                'network_layers': len(network_info['layers']),
                'layer_details': network_info['layers'],
                'f_function_activation': 'tanh',
                'g_function': 'identity'
            }
            
            if detailed_params:
                function_config_update['detailed_weights'] = detailed_params
            
            return (generation_mechanism, function_config_update), True
            
        except Exception as e:
            print(f"提取神经网络参数时出错 (节点 {node}): {e}")
            return FunctionParameterExtractor._create_error_result(node, func_type, parents, e), False
    
    @staticmethod
    def extract_generic_params(f_func, parents, node, func_type):
        """通用参数提取"""
        try:
            print(f"  尝试通用参数提取...")
            params = {}
            
            for attr_name in FunctionParameterExtractor.COMMON_ATTRS:
                if hasattr(f_func, attr_name):
                    attr_value = getattr(f_func, attr_name)
                    params[attr_name] = DataExtractor.convert_to_serializable(attr_value)
            
            # 特殊处理高斯过程函数
            if func_type == 'gaussian_process' and hasattr(f_func, 'kernel'):
                if hasattr(f_func.kernel, 'lengthscale'):
                    params['kernel_lengthscale'] = DataExtractor.convert_to_serializable(
                        f_func.kernel.lengthscale)
                if hasattr(f_func.kernel, 'variance'):
                    params['kernel_variance'] = DataExtractor.convert_to_serializable(
                        f_func.kernel.variance)
            
            if params:
                generation_mechanism = {
                    'expression': f"{node} = {func_type}({', '.join(parents)})",
                    'parameters': params,
                    'function_type': func_type,
                    'parents': parents
                }
                
                function_config_update = params.copy()
                function_config_update.update({
                    'f_function_type': func_type,
                    'g_function': 'identity'
                })
                
                return (generation_mechanism, function_config_update), True
            
            return None, False
            
        except Exception as e:
            print(f"通用参数提取失败 (节点 {node}): {e}")
            return FunctionParameterExtractor._create_error_result(node, func_type, parents, e), False
    
    @staticmethod
    def _create_error_result(node, func_type, parents, error):
        """创建错误结果"""
        return {
            'expression': f"{node} = {func_type}({', '.join(parents)})",
            'function_type': func_type,
            'parents': parents,
            'error': str(error)
        }, {}


class StatisticsCalculator:
    """统计计算器"""
    
    @staticmethod
    def format_mean_std(values, precision=4):
        """格式化均值±标准差"""
        if len(values) == 0:
            return None
        mean_val = values.mean()
        std_val = values.std() if len(values) > 1 else 0.0
        return f"{mean_val:.{precision}f}±{std_val:.{precision}f}"
    
    @staticmethod
    def calculate_pairwise_correlations_and_mi(data, feature_names):
        """计算数据集中所有特征之间的相关系数和互信息"""
        n_features = data.shape[1]
        
        # 检查方差为0的特征
        feature_variances = np.var(data, axis=0)
        zero_variance_features = np.where(feature_variances < 1e-10)[0]
        
        if len(zero_variance_features) > 0:
            print(f"检测到方差为0的特征: {[feature_names[i] for i in zero_variance_features]}")
        
        # 计算相关系数矩阵
        corr_matrix = np.corrcoef(data, rowvar=False)
        
        # 处理方差为0的特征
        for i in zero_variance_features:
            for j in range(n_features):
                if i != j:
                    corr_matrix[i, j] = 0.0001
                    corr_matrix[j, i] = 0.0001
        
        # 计算互信息矩阵
        mi_matrix = np.zeros((n_features, n_features))
        for i in range(n_features):
            for j in range(n_features):
                if i == j:
                    mi_matrix[i, j] = 1.0
                elif i in zero_variance_features or j in zero_variance_features:
                    mi_matrix[i, j] = 0.0001
                else:
                    try:
                        mi_value = mutual_info_regression(data[:, [i]], data[:, j])
                        mi_matrix[i, j] = (np.asarray(mi_value).item() 
                                         if np.asarray(mi_value).size == 1 
                                         else np.asarray(mi_value)[0])
                    except:
                        mi_matrix[i, j] = np.nan
        
        return {
            'correlation_matrix': corr_matrix,
            'mutual_info_matrix': mi_matrix,
            'feature_names': feature_names,
            'zero_variance_features': [feature_names[i] for i in zero_variance_features]
        }


class NodeRelationshipManager:
    """节点关系管理器"""

    @staticmethod
    def get_node_relationships_cached(dag_nodes, dag_edges, target_node, cache=None):
        """获取节点关系（带缓存）"""
        if cache is None:
            cache = {}

        cache_key = (tuple(sorted(dag_nodes)), tuple(sorted(dag_edges)), target_node)
        if cache_key in cache:
            return cache[cache_key]

        temp_dag = DAGManager.build_temp_dag(dag_nodes, dag_edges)
        if target_node not in temp_dag.nodes():
            return {}

        relationships = get_exclusive_node_relationships(temp_dag, target_node)
        cache[cache_key] = relationships
        return relationships

    @staticmethod
    def classify_node_type(node, target_node, relationships):
        """分类节点类型"""
        if node == target_node:
            return 'target'
        elif node in relationships.get('parents', []):
            return 'parent'
        elif node in relationships.get('children', []):
            return 'child'
        elif node in relationships.get('spouses', []):
            return 'spouse'
        else:
            return 'other'


class ConfigExtractor:
    """配置提取器"""

    @staticmethod
    def extract_config_info(scm):
        """提取配置信息"""
        if not hasattr(scm, 'config') or not scm.config:
            return {}

        config = scm.config
        return {
            'train_test_split_ratio': config.get('train_test_split_ratio', 0.5),
            'noise_config': {
                'min_noise_std': config.get('min_noise_std', 0.1),
                'max_noise_std': config.get('max_noise_std', 0.5),
                'noise_distribution': 'gaussian',
                'noise_mean': 0.0,
                'use_snr_method': config.get('use_snr_method', False),
                'snr_config': config.get('snr_config', {}),
                'use_monte_carlo_precompute': config.get('use_monte_carlo_precompute', False)
            },
            'root_distribution_config': {
                'root_distribution': config.get('root_distribution', 'gaussian'),
                'root_mean': config.get('root_mean', 0.0),
                'root_std': config.get('root_std', 1.0),
                'sample_root_std': config.get('sample_root_std', False),
                'min_root': config.get('min_root', 0.0),
                'max_root': config.get('max_root', 1.0),
                'sample_cause_ranges': config.get('sample_cause_ranges', False)
            }
        }


class SCMInfoExtractor:
    """SCM信息提取器主类"""

    def __init__(self):
        self.relationship_cache = {}

    def extract_serializable_scm_info(self, scm_objects, extract_function_weights=True):
        """从SCM对象中提取可序列化的信息"""
        serializable_info = {}

        for dataset_idx, scm in scm_objects.items():
            try:
                info = self._extract_single_scm_info(scm, extract_function_weights)
                info['dataset_idx'] = dataset_idx
                serializable_info[dataset_idx] = info
            except Exception as e:
                serializable_info[dataset_idx] = {
                    'dataset_idx': dataset_idx,
                    'error': f"Failed to extract SCM info: {str(e)}"
                }

        return serializable_info

    def _extract_single_scm_info(self, scm, extract_function_weights):
        """提取单个SCM的信息"""
        info = {
            'selected_features': DataExtractor.safe_extract_attr(scm, 'selected_features'),
            'selected_target': DataExtractor.safe_extract_attr(scm, 'selected_target'),
            'num_samples': DataExtractor.safe_extract_attr(scm, 'num_samples'),
            'config': ConfigExtractor.extract_config_info(scm)
        }

        # 提取DAG信息
        if hasattr(scm, 'dag'):
            info['dag_nodes'] = list(scm.dag.nodes())
            info['dag_edges'] = list(scm.dag.edges())

        # 提取其他信息
        self._extract_additional_info(scm, info)

        # 提取assignment信息
        if hasattr(scm, 'assignment') and scm.assignment:
            info['assignment_info'] = self._extract_assignment_info(
                scm, extract_function_weights)

        return info

    def _extract_additional_info(self, scm, info):
        """提取额外信息"""
        # DAG类型信息
        if hasattr(scm, 'dag_info') and scm.dag_info:
            info['dag_info'] = scm.dag_info
            info['dag_structure_type'] = scm.dag_info.get('structure_type', None)

        # SNR验证结果
        if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results:
            info['snr_validation_results'] = scm.snr_validation_results

        # 根节点分布信息
        info['root_distribution_info'] = DataExtractor.safe_call_method(
            scm, 'get_root_distribution_info', error_prefix="提取根节点分布信息时出错")

        # 节点统计信息
        info['node_statistics'] = DataExtractor.safe_call_method(
            scm, 'get_node_statistics', error_prefix="提取节点统计信息时出错")

        # 其他属性
        for attr in ['intervention_nodes', 'perturbation_nodes', 'unobserved_nodes']:
            value = DataExtractor.safe_extract_attr(scm, attr)
            if isinstance(value, (list, tuple, set)):
                info[attr] = list(value)
            elif value is not None:
                info[attr] = value

    def _extract_assignment_info(self, scm, extract_function_weights):
        """提取assignment信息"""
        assignment_info = {}

        for node, assignment_data in scm.assignment.items():
            if 'assignment' not in assignment_data:
                continue

            assignment_obj = assignment_data['assignment']
            node_info = {
                'parents': assignment_data.get('parents', [])
            }

            # 提取函数配置
            self._extract_function_config(assignment_obj, node_info)

            # 提取生成机制
            if extract_function_weights:
                self._extract_generation_mechanism(
                    assignment_obj, node_info, node)
            else:
                self._create_basic_generation_mechanism(node_info, node)

            assignment_info[node] = node_info

        return assignment_info

    def _extract_function_config(self, assignment_obj, node_info):
        """提取函数配置"""
        if hasattr(assignment_obj, 'custom_function_config') and assignment_obj.custom_function_config:
            func_config = assignment_obj.custom_function_config
            node_info['function_config'] = {
                "type": func_config.get('type', 'mlp'),
                "parents": node_info['parents']
            }

            # 保存函数参数
            for param_name, param_value in func_config.items():
                if param_name not in ['type', 'target_snr', 'noise_std']:
                    node_info['function_config'][param_name] = DataExtractor.convert_to_serializable(param_value)
        else:
            node_info['function_config'] = {
                "type": "mlp",
                "parents": node_info['parents']
            }

    def _extract_generation_mechanism(self, assignment_obj, node_info, node):
        """提取生成机制"""
        if not hasattr(assignment_obj, 'f_function') or assignment_obj.f_function is None:
            self._create_basic_generation_mechanism(node_info, node)
            return

        f_func = assignment_obj.f_function
        parents = node_info['parents']
        func_type = node_info['function_config'].get('type', 'mlp')

        print(f"正在提取节点 {node} 的函数参数，类型: {func_type}")

        # 尝试不同的参数提取方法
        extractors = [
            FunctionParameterExtractor.extract_linear_params,
            FunctionParameterExtractor.extract_neural_network_params,
            FunctionParameterExtractor.extract_generic_params
        ]

        for extractor in extractors:
            result, success = extractor(f_func, parents, node, func_type)
            if success and result:
                generation_mechanism, function_config_update = result
                node_info['generation_mechanism'] = generation_mechanism
                node_info['function_config'].update(function_config_update)
                return

        # 如果所有提取方法都失败，创建基本信息
        self._create_basic_generation_mechanism(node_info, node)

    def _create_basic_generation_mechanism(self, node_info, node):
        """创建基本生成机制信息"""
        func_type = node_info['function_config'].get('type', 'unknown')
        parents = node_info['parents']
        node_info['generation_mechanism'] = {
            'expression': f"{node} = {func_type}({', '.join(parents)})",
            'parents': parents,
            'function_type': func_type
        }


class FeatureImportanceProcessor:
    """特征重要性处理器"""

    @staticmethod
    def reorganize_feature_importance_by_node_type(importance_data, target_node, dag_nodes, dag_edges):
        """将特征重要性数据按节点类型重新组织"""
        if not importance_data or not target_node:
            return importance_data

        temp_dag = DAGManager.build_temp_dag(dag_nodes, dag_edges)
        if target_node not in temp_dag.nodes():
            return importance_data

        relationships = get_exclusive_node_relationships(temp_dag, target_node)
        reorganized_data = {}

        for importance_type in ['permutation', 'builtin']:
            if importance_type in importance_data:
                reorganized_data[importance_type] = {
                    'parents': {}, 'children': {}, 'spouses': {}, 'others': {}
                }

                for feature_name, importance_value in importance_data[importance_type].items():
                    if feature_name == target_node:
                        continue

                    node_type = NodeRelationshipManager.classify_node_type(
                        feature_name, target_node, relationships)

                    if node_type == 'parent':
                        reorganized_data[importance_type]['parents'][feature_name] = importance_value
                    elif node_type == 'child':
                        reorganized_data[importance_type]['children'][feature_name] = importance_value
                    elif node_type == 'spouse':
                        reorganized_data[importance_type]['spouses'][feature_name] = importance_value
                    else:
                        reorganized_data[importance_type]['others'][feature_name] = importance_value

        return reorganized_data


class DataProcessor:
    """数据处理器主类"""

    def __init__(self):
        self.scm_extractor = SCMInfoExtractor()
        self.relationship_cache = {}

    def process_comprehensive_data(self, scm_objects, results, importance_results, image_dir,
                                 custom_functions_configs=None, save_json=True, generate_plots=True,
                                 correlation_results=None, mutual_info_results=None):
        """处理综合数据并生成可视化"""
        try:
            print(f"开始处理综合数据...")
            print(f"文件保存设置: JSON={save_json}, 可视化={generate_plots}")

            # 创建结构化数据
            structured_data = self._create_structured_data(
                scm_objects, custom_functions_configs)

            # 处理数据集
            snr_plot_data = self._process_datasets(
                scm_objects, structured_data, results, importance_results,
                correlation_results, mutual_info_results, custom_functions_configs)

            # 保存数据
            if save_json:
                self._save_json_data(structured_data, image_dir)

            return {
                'structured_data': structured_data,
                'snr_plot_data': snr_plot_data
            }

        except Exception as e:
            print(f"处理综合数据时出错: {str(e)}")
            traceback.print_exc()
            return None

    def _create_structured_data(self, scm_objects, custom_functions_configs):
        """创建结构化数据"""
        sample_config = self._get_sample_config(scm_objects)

        return {
            "metadata": {
                "total_datasets": len(scm_objects),
                "total_configs": len(custom_functions_configs) if custom_functions_configs else 0,
                "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "train_test_split": sample_config.get('train_test_split_ratio', 0.5),
                "noise_config": sample_config.get('noise_config', self._get_default_noise_config()),
                "root_distribution_config": sample_config.get('root_distribution_config',
                                                            self._get_default_root_config())
            },
            "results": {}
        }

    def _get_sample_config(self, scm_objects):
        """获取样本配置"""
        if not scm_objects:
            return {}

        first_dataset = next(iter(scm_objects.values()))
        if not first_dataset:
            return {}

        first_scm_info = next(iter(first_dataset.values()))
        return first_scm_info.get('config', {})

    def _get_default_noise_config(self):
        """获取默认噪声配置"""
        return {
            "min_noise_std": 0.1,
            "max_noise_std": 0.5,
            "use_snr_method": False,
            "snr_config": {},
            "use_monte_carlo_precompute": False
        }

    def _get_default_root_config(self):
        """获取默认根节点配置"""
        return {
            "root_distribution": 'gaussian',
            "root_mean": 0.0,
            "root_std": 1.0,
            "sample_root_std": False,
            "min_root": 0.0,
            "max_root": 1.0,
            "sample_cause_ranges": False
        }

    def _process_datasets(self, scm_objects, structured_data, results, importance_results,
                         correlation_results, mutual_info_results, custom_functions_configs):
        """处理数据集"""
        snr_plot_data = []
        total_datasets = len(scm_objects)
        processed_datasets = 0

        print(f"总共需要处理 {total_datasets} 个数据集")

        for dataset_idx, configs in scm_objects.items():
            processed_datasets += 1
            if processed_datasets % 10 == 0 or processed_datasets == total_datasets:
                print(f"处理进度: {processed_datasets}/{total_datasets} 数据集")

            for config_key, scm_info in configs.items():
                config_type_name = self._get_config_type_name(config_key, custom_functions_configs)

                # 初始化配置结构
                self._initialize_config_structure(structured_data, config_type_name)

                # 处理单个数据集
                dataset_snr_data = self._process_single_dataset(
                    structured_data, config_type_name, dataset_idx, scm_info,
                    results, importance_results, correlation_results, mutual_info_results)

                snr_plot_data.extend(dataset_snr_data)

        return snr_plot_data

    def _get_config_type_name(self, config_key, custom_functions_configs):
        """获取配置类型名称"""
        if custom_functions_configs and config_key in custom_functions_configs:
            return custom_functions_configs[config_key]['name']
        return config_key

    def _initialize_config_structure(self, structured_data, config_type_name):
        """初始化配置结构"""
        if config_type_name not in structured_data["results"]:
            structured_data["results"][config_type_name] = {
                "config_info": {
                    "name": config_type_name,
                    "description": f"配置: {config_type_name}"
                },
                "datasets": {}
            }

    def _process_single_dataset(self, structured_data, config_type_name, dataset_idx, scm_info,
                               results, importance_results, correlation_results, mutual_info_results):
        """处理单个数据集"""
        dataset_name = f"dataset_{dataset_idx}"

        # 初始化数据集结构
        if dataset_name not in structured_data["results"][config_type_name]["datasets"]:
            structured_data["results"][config_type_name]["datasets"][dataset_name] = {
                "dataset_info": {},
                "nodes": OrderedDict(),
                "model_results": {}
            }

        current_dataset = structured_data["results"][config_type_name]["datasets"][dataset_name]

        # 填充数据集基础信息
        self._fill_dataset_info(current_dataset, scm_info)

        # 处理节点信息
        snr_data = self._process_nodes(current_dataset, scm_info, config_type_name, dataset_idx,
                                     correlation_results, mutual_info_results)

        # 处理模型结果
        self._process_model_results(current_dataset, dataset_idx, config_type_name,
                                  results, importance_results, scm_info)

        return snr_data

    def _fill_dataset_info(self, current_dataset, scm_info):
        """填充数据集基础信息"""
        if current_dataset["dataset_info"]:  # 已经填充过
            return

        current_dataset["dataset_info"] = {
            'num_samples': scm_info.get('num_samples', 'N/A'),
            'num_features': len(scm_info.get('selected_features', [])),
            'num_nodes': len(scm_info.get('dag_nodes', [])) if 'dag_nodes' in scm_info else 'N/A'
        }

        # 添加根节点分布信息
        if 'root_distribution_info' in scm_info and isinstance(scm_info['root_distribution_info'], dict):
            root_dist_info = scm_info['root_distribution_info']
            current_dataset["dataset_info"].update({
                'root_distribution': root_dist_info.get('distribution_type', 'N/A'),
                'root_mean': root_dist_info.get('mean', 'N/A'),
                'root_actual_std': root_dist_info.get('std', 'N/A'),
                'root_sample_std': root_dist_info.get('sample_std', 'N/A'),
                'root_config_std': root_dist_info.get('configured_std', 'N/A'),
                'root_min': root_dist_info.get('min_root', 'N/A'),
                'root_max': root_dist_info.get('max_root', 'N/A'),
                'sample_cause_ranges': root_dist_info.get('sample_cause_ranges', 'N/A')
            })

    def _process_nodes(self, current_dataset, scm_info, config_type_name, dataset_idx,
                      correlation_results=None, mutual_info_results=None):
        """处理节点信息"""
        snr_data = []

        # 获取所有节点和拓扑序
        ordered_nodes_with_types = self._get_ordered_nodes(scm_info)

        for node, node_type in ordered_nodes_with_types:
            if node not in current_dataset["nodes"]:
                current_dataset["nodes"][node] = {
                    "node_type": node_type,
                    "function_config": {},
                    "snr_info": {},
                    "data_statistics": {},
                    "correlation_info": {},
                    "mutual_info": {}
                }

            current_node = current_dataset["nodes"][node]
            current_node["node_type"] = node_type

            # 处理函数配置、SNR信息、统计信息等
            self._fill_node_info(current_node, node, scm_info)

            # 收集SNR数据
            snr_info = current_node.get("snr_info", {})
            actual_snr = snr_info.get("actual_snr")
            if actual_snr is not None:
                snr_data.append({
                    'config_type': config_type_name,
                    'node_name': node,
                    'actual_snr': actual_snr,
                    'dataset_id': dataset_idx
                })

        return snr_data

    def _get_ordered_nodes(self, scm_info):
        """获取排序的节点列表"""
        all_nodes = set()
        if 'dag_nodes' in scm_info:
            all_nodes.update(scm_info['dag_nodes'])
        if 'snr_validation_results' in scm_info and scm_info['snr_validation_results']:
            all_nodes.update(scm_info['snr_validation_results'].get('target_snr', {}).keys())
        if 'node_statistics' in scm_info and isinstance(scm_info['node_statistics'], dict):
            all_nodes.update(scm_info['node_statistics'].keys())

        # 按拓扑序排列节点
        ordered_nodes_with_types = []
        if 'dag_nodes' in scm_info and 'dag_edges' in scm_info:
            topo_order = DAGManager.get_topological_order(
                scm_info['dag_nodes'], scm_info['dag_edges'], all_nodes)

            target_node = scm_info.get('selected_target')
            relationships = {}

            if target_node:
                relationships = NodeRelationshipManager.get_node_relationships_cached(
                    scm_info['dag_nodes'], scm_info['dag_edges'], target_node, self.relationship_cache)

            for node in topo_order:
                node_type = NodeRelationshipManager.classify_node_type(node, target_node, relationships)
                ordered_nodes_with_types.append((node, node_type))
        else:
            for node in sorted(all_nodes):
                ordered_nodes_with_types.append((node, 'other'))

        return ordered_nodes_with_types

    def _fill_node_info(self, current_node, node, scm_info):
        """填充节点信息"""
        # 获取根节点
        root_nodes = set()
        if 'dag_nodes' in scm_info and 'dag_edges' in scm_info:
            all_dag_nodes = set(scm_info['dag_nodes'])
            nodes_with_parents = {child for _, child in scm_info['dag_edges']}
            root_nodes = all_dag_nodes - nodes_with_parents

        # 节点函数配置
        if node in root_nodes:
            current_node["function_config"] = None
        elif 'assignment_info' in scm_info and node in scm_info['assignment_info']:
            assignment_node_info = scm_info['assignment_info'][node]
            current_node["function_config"] = assignment_node_info.get('function_config', {"type": "mlp"})
        else:
            current_node["function_config"] = {"type": "mlp"}

        # SNR信息
        if 'snr_validation_results' in scm_info and scm_info['snr_validation_results']:
            snr_results_data = scm_info['snr_validation_results']
            current_node["snr_info"] = {
                "target_snr": snr_results_data.get('target_snr', {}).get(node),
                "configured_noise_std": snr_results_data.get('configured_noise_std', {}).get(node),
                "actual_noise_std": snr_results_data.get('actual_noise_std', {}).get(node),
                "actual_snr": snr_results_data.get('actual_snr', {}).get(node),
                "signal_var": snr_results_data.get('signal_var', {}).get(node),
                "signal_mean": snr_results_data.get('signal_mean', {}).get(node),
                "config_noise_mean": snr_results_data.get('config_noise_mean', {}).get(node),
                "calculated_noise_mean_target": snr_results_data.get('calculated_noise_mean_target', {}).get(node),
                "actual_noise_mean": snr_results_data.get('actual_noise_mean', {}).get(node)
            }

        # 节点统计信息
        if 'node_statistics' in scm_info and isinstance(scm_info['node_statistics'], dict):
            node_stats = scm_info['node_statistics']
            if node in node_stats:
                stats = node_stats[node]
                current_node["data_statistics"] = {
                    'min': stats['min'], 'q25': stats['q25'], 'median': stats['median'],
                    'q75': stats['q75'], 'max': stats['max'], 'mean': stats['mean'], 'std': stats['std']
                }

    def _process_model_results(self, current_dataset, dataset_idx, config_key,
                              results, importance_results, scm_info):
        """处理模型结果"""
        expected_dataset_name = f'dataset_{dataset_idx}'
        dataset_results = [r for r in results
                          if r['dataset'] == expected_dataset_name and r['use_snr'] == config_key]

        for result in dataset_results:
            model_type = result['model_type']

            if model_type not in current_dataset["model_results"]:
                current_dataset["model_results"][model_type] = {
                    "performance": {},
                    "feature_importance": {}
                }

            current_model = current_dataset["model_results"][model_type]
            current_model["performance"] = {
                'r2_train': result['r2_train'], 'r2_test': result['r2_test'],
                'r2_intervention': result['r2_intv'], 'rmse_train': result['rmse_train'],
                'rmse_test': result['rmse_test'], 'rmse_intervention': result['rmse_intv'],
                'mape_train': result['mape_train'], 'mape_test': result['mape_test'],
                'mape_intervention': result['mape_intv']
            }

            # 处理特征重要性
            importance_data = [imp for imp in importance_results
                             if (imp['dataset'] == expected_dataset_name and
                                 imp['use_snr'] == config_key and
                                 imp['model_type'] == model_type)]

            if importance_data:
                imp_result = importance_data[0]
                target_node = scm_info.get('selected_target')
                dag_nodes = scm_info.get('dag_nodes', [])
                dag_edges = scm_info.get('dag_edges', [])

                reorganized_importance = FeatureImportanceProcessor.reorganize_feature_importance_by_node_type(
                    imp_result['importance'], target_node, dag_nodes, dag_edges)
                current_model["feature_importance"] = reorganized_importance

    def _save_json_data(self, structured_data, image_dir):
        """保存JSON数据"""
        json_path = os.path.join(image_dir, 'comprehensive_results.json')

        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return super(NumpyEncoder, self).default(obj)

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(structured_data, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
        print(f"已保存层次化JSON结果到: {json_path}")


class CSVSummaryGenerator:
    """CSV汇总生成器"""

    def save_summary_csv(self, image_dir, structured_data, custom_dag_type=None,
                        dag_generation_seeds=None, results=None, r2_threshold=0.5,
                        annotate_all_datasets=True, annotate_effective_datasets=True,
                        effective_datasets=None):
        """生成汇总CSV文件"""
        if not structured_data or 'results' not in structured_data:
            print("没有数据可用于生成可视化汇总CSV文件")
            return

        try:
            print(f"生成汇总CSV")
            print(f"保存设置: 所有数据集={annotate_all_datasets}, 有效数据集={annotate_effective_datasets}")

            if not annotate_all_datasets and not annotate_effective_datasets:
                print("警告：两个参数都为False，将不生成任何CSV文件")
                return

            # 构建DataFrame数据
            df = self._build_dataframe(structured_data)
            if df.empty:
                print("comprehensive_results.csv文件为空")
                return

            # 使用传入的有效数据集
            if effective_datasets is None:
                raise ValueError("effective_datasets 参数为 None，应该从主调用文件中预计算并传入")
            else:
                print(f"使用传入的有效数据集: {len(effective_datasets)} 个配置")

            # 检查是否所有配置都完全有效（使用eff_equ_all属性）
            all_configs_effective = True
            effective_datasets_count = 0

            for config_name in effective_datasets:
                effective_data = effective_datasets[config_name]

                # 现在effective_datasets[config_name]总是字典结构
                eff_count = len(effective_data.get('datasets', set()))
                eff_equ_all = effective_data.get('eff_equ_all', False)

                effective_datasets_count += eff_count
                if not eff_equ_all:
                    all_configs_effective = False

            # 自动调整参数：如果所有配置都完全有效，则不需要单独标注有效数据集
            auto_adjusted = all_configs_effective and effective_datasets_count > 0
            if auto_adjusted:
                print("所有配置下的数据集都是有效数据集，将只保存全部数据集结果")
                annotate_effective_datasets = False

            # 确定分组方式
            skip_node_level_info = self._should_skip_node_level_info(
                custom_dag_type, dag_generation_seeds)

            # 生成汇总统计
            summary_df = self._generate_summary_statistics(
                df, effective_datasets, skip_node_level_info)

            # 保存CSV文件
            return self._save_csv_files(
                summary_df, image_dir, annotate_all_datasets,
                annotate_effective_datasets, effective_datasets,
                auto_adjusted, skip_node_level_info)

        except Exception as e:
            print(f"生成汇总CSV文件时出错: {str(e)}")
            traceback.print_exc()
            return None

    def _build_dataframe(self, structured_data):
        """构建DataFrame数据"""
        data_rows = []

        for config_name, config_data in structured_data['results'].items():
            datasets = config_data.get('datasets', {})

            for dataset_name, dataset_info in datasets.items():
                dataset_id = int(dataset_name.split('_')[1])

                base_info = {
                    'dataset_id': dataset_id,
                    'config_key': config_name,
                    'config_type': config_name,
                    'num_samples': dataset_info['dataset_info'].get('num_samples', 'N/A'),
                    'num_features': dataset_info['dataset_info'].get('num_features', 'N/A'),
                    'num_nodes': dataset_info['dataset_info'].get('num_nodes', 'N/A')
                }

                # 添加根节点分布信息
                root_info = {}
                for key in ['root_distribution', 'root_mean', 'root_actual_std', 'root_sample_std',
                           'root_config_std', 'root_min', 'root_max', 'sample_cause_ranges']:
                    if key in dataset_info['dataset_info']:
                        root_info[key] = dataset_info['dataset_info'][key]

                # 为每个节点和每个模型创建行数据
                nodes = dataset_info.get('nodes', {})
                model_results = dataset_info.get('model_results', {})

                for node_name, node_info in nodes.items():
                    for model_type, model_data in model_results.items():
                        row_data = base_info.copy()
                        row_data.update(root_info)
                        row_data['dataset_name'] = dataset_name  # 添加dataset_name列
                        row_data['node_name'] = node_name
                        row_data['model_type'] = model_type
                        row_data['node_type'] = node_info.get('node_type', 'other')

                        # 添加函数配置信息
                        func_config = node_info.get('function_config')
                        if func_config is None:
                            row_data['function_type'] = None
                        else:
                            row_data['function_type'] = func_config.get('type', 'mlp')
                            for param_name, param_value in func_config.items():
                                if param_name != 'type':
                                    row_data[f'func_{param_name}'] = param_value

                        # 添加SNR信息
                        snr_info = node_info.get('snr_info', {})
                        target_snr = snr_info.get('target_snr')
                        configured_noise_std = snr_info.get('configured_noise_std')

                        if target_snr is not None:
                            row_data.update({
                                'target_snr': target_snr,
                                'config_noise_std': None,
                                'calculated_noise_std_sample': None,
                                'calculated_noise_std_target': configured_noise_std,
                                'configured_noise_std': configured_noise_std,
                                'actual_noise_std': snr_info.get('actual_noise_std'),
                                'actual_snr': snr_info.get('actual_snr'),
                                'signal_var': snr_info.get('signal_var'),
                                'signal_mean': snr_info.get('signal_mean'),
                                'config_noise_mean': snr_info.get('config_noise_mean'),
                                'calculated_noise_mean_target': snr_info.get('calculated_noise_mean_target'),
                                'actual_noise_mean': snr_info.get('actual_noise_mean')
                            })
                        else:
                            row_data.update({
                                'target_snr': None,
                                'config_noise_std': configured_noise_std,
                                'calculated_noise_std_sample': None,
                                'calculated_noise_std_target': None,
                                'configured_noise_std': configured_noise_std,
                                'actual_noise_std': snr_info.get('actual_noise_std'),
                                'actual_snr': snr_info.get('actual_snr'),
                                'signal_var': snr_info.get('signal_var'),
                                'signal_mean': snr_info.get('signal_mean'),
                                'config_noise_mean': snr_info.get('config_noise_mean'),
                                'calculated_noise_mean_target': snr_info.get('calculated_noise_mean_target'),
                                'actual_noise_mean': snr_info.get('actual_noise_mean')
                            })

                        # 添加数据统计信息
                        data_stats = node_info.get('data_statistics', {})
                        for stat_key, stat_value in data_stats.items():
                            row_data[f'data_{stat_key}'] = stat_value

                        # 添加模型性能信息
                        performance = model_data.get('performance', {})
                        row_data.update({
                            'r2_train': performance.get('r2_train'),
                            'r2_test': performance.get('r2_test'),
                            'r2_intervention': performance.get('r2_intervention'),
                            'rmse_train': performance.get('rmse_train'),
                            'rmse_test': performance.get('rmse_test'),
                            'rmse_intervention': performance.get('rmse_intervention'),
                            'mape_train': performance.get('mape_train'),
                            'mape_test': performance.get('mape_test'),
                            'mape_intervention': performance.get('mape_intervention')
                        })

                        # 添加特征重要性信息
                        feature_importance = model_data.get('feature_importance', {})
                        if 'permutation' in feature_importance:
                            for _, importance_dict in feature_importance['permutation'].items():
                                for feature_name, importance_value in importance_dict.items():
                                    row_data[f'perm_importance_{feature_name}'] = importance_value
                        if 'builtin' in feature_importance:
                            for _, importance_dict in feature_importance['builtin'].items():
                                for feature_name, importance_value in importance_dict.items():
                                    row_data[f'builtin_importance_{feature_name}'] = importance_value

                        data_rows.append(row_data)

        return pd.DataFrame(data_rows)

    def _should_skip_node_level_info(self, custom_dag_type, dag_generation_seeds):
        """判断是否跳过节点级别信息"""
        if custom_dag_type in ['random_ER', 'random_SF', None]:
            has_specific_dag_seeds = (dag_generation_seeds is not None and
                                    isinstance(dag_generation_seeds, list) and
                                    len(dag_generation_seeds) > 0)
            return not has_specific_dag_seeds
        return False

    def _generate_summary_statistics(self, df, effective_datasets, skip_node_level_info):
        """生成汇总统计"""
        if skip_node_level_info:
            groupby_columns = ['config_type', 'model_type']
        else:
            groupby_columns = ['config_type', 'node_name', 'model_type']

        performance_columns = ['r2_train', 'r2_test', 'r2_intervention',
                              'rmse_train', 'rmse_test', 'rmse_intervention',
                              'mape_train', 'mape_test', 'mape_intervention']

        # 定义SNR相关列
        snr_columns = [
            'target_snr', 'config_noise_std', 'calculated_noise_std_sample', 'calculated_noise_std_target',
            'actual_noise_std', 'actual_snr', 'signal_var', 'signal_mean', 'config_noise_mean',
            'calculated_noise_mean_target', 'actual_noise_mean'
        ]

        # 找到permutation importance列
        perm_importance_columns = [col for col in df.columns if col.startswith('perm_importance_')]

        # 检查哪些列存在
        available_performance_columns = [col for col in performance_columns if col in df.columns]
        available_snr_columns = [col for col in snr_columns if col in df.columns]

        print(f"可用的性能指标列: {available_performance_columns}")
        print(f"可用的SNR相关列: {available_snr_columns}")
        print(f"可用的permutation importance列: {perm_importance_columns}")

        summary_rows = []

        for group_key, group_df in df.groupby(groupby_columns):
            if skip_node_level_info:
                config_type, model_type = group_key
                row_data = {
                    'config_type': config_type,
                    'model_type': model_type,
                    'n_datasets': len(group_df['dataset_id'].unique())
                }
            else:
                config_type, node_name, model_type = group_key
                row_data = {
                    'config_type': config_type,
                    'node_name': node_name,
                    'model_type': model_type,
                    'n_datasets': len(group_df['dataset_id'].unique())
                }

            # 只有在不跳过节点级别信息时才统计SNR相关指标
            if not skip_node_level_info:
                for col in available_snr_columns:
                    if col in group_df.columns:
                        values = group_df[col].dropna()
                        if len(values) > 0:
                            row_data[col] = StatisticsCalculator.format_mean_std(values)

            # 统计性能指标
            for col in available_performance_columns:
                if col in group_df.columns:
                    values = group_df[col].dropna()
                    if len(values) > 0:
                        row_data[col] = StatisticsCalculator.format_mean_std(values)

                        # 计算有效数据集上的性能指标
                        if effective_datasets and config_type in effective_datasets:
                            effective_data = effective_datasets[config_type]
                            effective_dataset_names = effective_data.get('datasets', set())

                            if 'dataset_name' in group_df.columns:
                                # 构建dataset_name列（如果不存在）
                                if 'dataset_name' not in group_df.columns and 'dataset_id' in group_df.columns:
                                    group_df = group_df.copy()
                                    group_df['dataset_name'] = group_df['dataset_id'].apply(lambda x: f'dataset_{x}')

                                effective_mask = group_df['dataset_name'].isin(effective_dataset_names)
                                effective_values = group_df[effective_mask][col].dropna()

                                if len(effective_values) > 0:
                                    row_data[f"{col}_effective"] = StatisticsCalculator.format_mean_std(effective_values)
                                    row_data[f"{col}_effective_count"] = len(effective_values)

            # 只有在不跳过节点级别信息时才统计permutation importance
            if not skip_node_level_info:
                for col in perm_importance_columns:
                    if col in group_df.columns:
                        values = group_df[col].dropna()
                        if len(values) > 0:
                            row_data[col] = StatisticsCalculator.format_mean_std(values)

            summary_rows.append(row_data)

        return pd.DataFrame(summary_rows)

    def _save_csv_files(self, summary_df, image_dir, annotate_all_datasets,
                       annotate_effective_datasets, effective_datasets,
                       auto_adjusted, skip_node_level_info):
        """保存CSV文件"""
        saved_files = []

        if annotate_all_datasets:
            # 保存所有数据集的汇总CSV文件
            all_datasets_path = os.path.join(image_dir, 'visualization_summary_all_datasets.csv')

            # 创建只包含所有数据集统计信息的DataFrame
            all_datasets_df = summary_df.copy()
            # 移除有效数据集相关的列
            effective_cols = [col for col in all_datasets_df.columns if '_effective' in col]
            if effective_cols:
                all_datasets_df = all_datasets_df.drop(columns=effective_cols)

            all_datasets_df.to_csv(all_datasets_path, index=False, encoding='utf-8')
            saved_files.append(all_datasets_path)

            if auto_adjusted:
                print(f"已保存所有数据集汇总CSV文件到: {all_datasets_path} (所有数据集都有效，无需单独保存有效数据集)")
            else:
                print(f"已保存所有数据集汇总CSV文件到: {all_datasets_path}")
            print(f"文件包含 {len(all_datasets_df)} 行数据，{len(all_datasets_df.columns)} 列")

        if annotate_effective_datasets and effective_datasets:
            # 保存有效数据集的汇总CSV文件
            effective_datasets_path = os.path.join(image_dir, 'visualization_summary_effective_datasets.csv')

            # 创建只包含有效数据集统计信息的DataFrame
            effective_datasets_df = summary_df.copy()
            # 只保留有效数据集相关的列和基础信息列
            base_cols = ['config_type', 'model_type', 'n_datasets']
            if not skip_node_level_info:
                base_cols.append('node_name')

            effective_cols = [col for col in effective_datasets_df.columns if '_effective' in col]
            keep_cols = base_cols + effective_cols

            if effective_cols:  # 只有当有有效数据集列时才保存
                # 过滤出存在的列
                available_keep_cols = [col for col in keep_cols if col in effective_datasets_df.columns]
                effective_datasets_df = effective_datasets_df[available_keep_cols]

                # 重命名列，移除_effective后缀以便于阅读
                rename_dict = {}
                for col in effective_cols:
                    if col.endswith('_effective'):
                        new_name = col.replace('_effective', '')
                        rename_dict[col] = new_name
                    elif col.endswith('_effective_count'):
                        new_name = col.replace('_effective_count', '_count')
                        rename_dict[col] = new_name

                effective_datasets_df = effective_datasets_df.rename(columns=rename_dict)

                effective_datasets_df.to_csv(effective_datasets_path, index=False, encoding='utf-8')
                saved_files.append(effective_datasets_path)
                print(f"已保存有效数据集汇总CSV文件到: {effective_datasets_path}")
                print(f"文件包含 {len(effective_datasets_df)} 行数据，{len(effective_datasets_df.columns)} 列")
            else:
                print("警告：没有有效数据集统计信息可保存")
        elif auto_adjusted:
            print("跳过有效数据集CSV文件保存（所有数据集都有效，已包含在全部数据集文件中）")

        # 如果两个都要保存，也保存一个完整版本（向后兼容）
        if annotate_all_datasets and annotate_effective_datasets:
            complete_path = os.path.join(image_dir, 'visualization_summary_complete.csv')
            summary_df.to_csv(complete_path, index=False, encoding='utf-8')
            saved_files.append(complete_path)
            print(f"已保存完整汇总CSV文件到: {complete_path}")
            print(f"文件包含 {len(summary_df)} 行数据，{len(summary_df.columns)} 列")

        return saved_files if saved_files else None


# 主要的重构函数接口，保持与原文件兼容
def extract_serializable_scm_info(scm_objects, extract_function_weights=True):
    """提取可序列化的SCM信息"""
    extractor = SCMInfoExtractor()
    return extractor.extract_serializable_scm_info(scm_objects, extract_function_weights)


def should_draw_dag_shared(scm, shared_drawn_dag_signatures):
    """检查是否应该绘制DAG图"""
    return DAGManager.should_draw_dag_shared(scm, shared_drawn_dag_signatures)


def calculate_pairwise_correlations_and_mi(data, feature_names):
    """计算相关系数和互信息"""
    return StatisticsCalculator.calculate_pairwise_correlations_and_mi(data, feature_names)


def reorganize_feature_importance_by_node_type(importance_data, target_node, dag_nodes, dag_edges):
    """重组特征重要性数据"""
    return FeatureImportanceProcessor.reorganize_feature_importance_by_node_type(
        importance_data, target_node, dag_nodes, dag_edges)


def process_comprehensive_data(scm_objects, results, importance_results, image_dir,
                              custom_functions_configs=None, save_json=True, generate_plots=True,
                              correlation_results=None, mutual_info_results=None):
    """处理综合数据"""
    processor = DataProcessor()
    return processor.process_comprehensive_data(
        scm_objects, results, importance_results, image_dir,
        custom_functions_configs, save_json, generate_plots,
        correlation_results, mutual_info_results)


def save_summary_csv(image_dir, structured_data, custom_dag_type=None, dag_generation_seeds=None,
                    results=None, r2_threshold=0.5, annotate_all_datasets=True,
                    annotate_effective_datasets=True, effective_datasets=None):
    """保存汇总CSV"""
    generator = CSVSummaryGenerator()
    return generator.save_summary_csv(
        image_dir, structured_data, custom_dag_type, dag_generation_seeds,
        results, r2_threshold, annotate_all_datasets, annotate_effective_datasets,
        effective_datasets)
