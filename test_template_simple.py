"""
简单的模板系统测试
"""

import torch
from scm_data_generator import generate_datasets

def test_template_system():
    """测试模板系统的基本功能"""
    
    # 基础配置
    h_config = {
        'device': 'cpu',
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'min_root': 0.0,
        'max_root': 1.0,
        'max_range': 0.5,
        'sample_cause_ranges': False,
        'sample_std': False,
        'min_num_samples': 1000,
        'max_num_samples': 1000,
        'train_test_split_ratio': 0.7,
        'task': 'regression',
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        'use_monte_carlo_precompute': False
    }
    
    # 自定义函数配置
    custom_functions = {
        'target': {
            'function_type': 'linear',
            'noise_type': 'gaussian'
        },
        'target_child': {
            'function_type': 'mlp',
            'noise_type': 'gaussian'
        },
        'Other_type': {  # 注意：这里使用 Other_type 而不是 other
            'function_type': 'mlp',
            'noise_type': 'gaussian'
        }
    }
    
    print("=" * 60)
    print("测试模板系统")
    print("=" * 60)
    
    # 步骤1: 生成模板（跳过数据生成）
    print("\n1. 生成模板（跳过数据生成）...")
    templates = generate_datasets(
        num_dataset=2,
        h_config=h_config,
        custom_dag_type='random_SF',
        custom_dag_size='small',
        perturbation_type='counterfactual',
        perturbation_node_type='all_non_family',
        perturbation_value_method='sample',
        custom_functions=custom_functions,
        seed=42,
        skip_data_generation=True  # 关键参数：跳过数据生成
    )
    
    print(f"✅ 生成了 {len(templates)} 个模板")
    
    # 验证模板结构
    for i, template in enumerate(templates):
        print(f"   模板 {i}: DAG节点数={len(template['dag'].nodes())}, "
              f"目标节点={template['selected_target']}, "
              f"特征数={len(template['selected_features'])}")
    
    # 步骤2: 基于模板生成数据
    print("\n2. 基于模板生成数据...")
    datasets = generate_datasets(
        num_dataset=4,  # 生成4个数据集，会循环使用2个模板
        h_config=h_config,
        perturbation_type='counterfactual',
        perturbation_node_type='all_non_family',
        perturbation_value_method='sample',
        custom_functions=custom_functions,
        seed=42,
        templates=templates  # 使用模板
    )
    
    print(f"✅ 基于模板生成了 {len(datasets)} 个数据集")
    
    # 验证数据集
    for i, dataset in enumerate(datasets):
        template_idx = i % len(templates)
        dataset_name, x_orig, y_orig, x_pert, y_pert, scm, data_info = dataset
        print(f"   数据集 {i} (基于模板{template_idx}): "
              f"原始数据={x_orig.shape}, 扰动数据={x_pert.shape}")
    
    print("\n✅ 模板系统测试完成！")
    return templates, datasets

if __name__ == '__main__':
    templates, datasets = test_template_system()
