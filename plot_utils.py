import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from scm_utils import get_exclusive_node_relationships
import networkx as nx


#-------------------------------------------------------------feature importance---------------------------------------------------
def collect_node_type_data_for_config(config_type, config_model_data, scm_objects, preferred_model_order):
    """
    收集指定配置下所有数据集的节点类型数据

    Returns:
        dict: {
            'markov_vs_others': {model_type: {'markov': [values], 'others': [values]}},
            'detailed_types': {model_type: {'parents': [values], 'children': [values], 'spouses': [values], 'others': [values]}}
        }
    """
    markov_vs_others_data = {}
    detailed_types_data = {}

    # 获取该配置下可用的模型类型
    available_models = [model for model in preferred_model_order if model in config_model_data]

    for model_type in available_models:
        markov_vs_others_data[model_type] = {'markov': [], 'others': []}
        detailed_types_data[model_type] = {'parents': [], 'children': [], 'spouses': [], 'others': []}

        model_data = config_model_data[model_type]

        # 遍历该模型下的所有数据集
        for dataset_idx, feature_importance in model_data.items():
            if dataset_idx not in scm_objects:
                continue

            dataset_scm_configs = scm_objects[dataset_idx]
            if config_type not in dataset_scm_configs:
                continue

            scm_info = dataset_scm_configs[config_type]

            # 获取目标节点和节点关系
            target_node = scm_info.get('selected_target', None)
            if not target_node or 'dag_nodes' not in scm_info or 'dag_edges' not in scm_info:
                continue

            # 构建图并获取节点关系
            temp_dag = nx.DiGraph()
            temp_dag.add_nodes_from(scm_info['dag_nodes'])
            temp_dag.add_edges_from(scm_info['dag_edges'])

            if target_node not in temp_dag.nodes():
                continue


            relationships = get_exclusive_node_relationships(temp_dag, target_node)

            # 分类节点并收集特征重要性
            for feature_name, importance_value in feature_importance.items():
                if feature_name == target_node:
                    continue  # 跳过目标节点本身

                # 确定节点类型（按优先级）
                if feature_name in relationships['parents']:
                    node_type = 'parents'
                    is_markov = True
                elif feature_name in relationships['children']:
                    node_type = 'children'
                    is_markov = True
                elif feature_name in relationships['spouses']:
                    node_type = 'spouses'
                    is_markov = True
                else:
                    node_type = 'others'
                    is_markov = False

                # 添加到详细分类数据
                detailed_types_data[model_type][node_type].append(importance_value)

                # 添加到马尔科夫分类数据
                if is_markov:
                    markov_vs_others_data[model_type]['markov'].append(importance_value)
                else:
                    markov_vs_others_data[model_type]['others'].append(importance_value)

    return {
        'markov_vs_others': markov_vs_others_data,
        'detailed_types': detailed_types_data
    }


def plot_permutation_importance_comparison(importance_results, image_dir, scm_objects=None, custom_functions_configs=None,
                                         results=None, r2_threshold=0.5, annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    按节点类型分组绘制不同SNR配置不同模型下permutation_importance的箱线图
    每个配置单独一张画布，包含两个子图：
    1. 马尔科夫节点 vs 其他节点
    2. 父节点、子节点、配偶节点、其他节点

    Args:
        importance_results: 特征重要性结果列表
        image_dir: 图像保存目录
        scm_objects: SCM对象字典
        custom_functions_configs: 自定义函数配置字典
        results: 模型结果列表，用于计算有效数据集
        r2_threshold: R2阈值，用于筛选有效数据集
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    if not importance_results:
        print("没有特征重要性数据，跳过permutation importance对比图生成")
        return

    if scm_objects is None:
        print("警告: 未提供scm_objects，无法按节点类型分组")
        return

    # 计算有效数据集（如果提供了results）
    effective_datasets = {}
    if results is not None:
        _, effective_datasets = calculate_effective_datasets(results, r2_threshold)

    # 按配置类型分组收集数据
    config_data = {}  # {config_type: {model_type: {dataset_idx: {feature_name: importance_value}}}}
    config_data_effective = {}  # 有效数据集的数据

    for result in importance_results:
        config_type = result.get('use_snr', 'unknown')
        model_type = result.get('model_type', 'unknown')
        dataset_name = result.get('dataset', 'unknown')

        # 从dataset_name中提取dataset_idx
        try:
            dataset_idx = int(dataset_name.split('_')[1])
        except:
            continue

        if config_type not in config_data:
            config_data[config_type] = {}
            config_data_effective[config_type] = {}
        if model_type not in config_data[config_type]:
            config_data[config_type][model_type] = {}
            config_data_effective[config_type][model_type] = {}
        if dataset_idx not in config_data[config_type][model_type]:
            config_data[config_type][model_type][dataset_idx] = {}
        if dataset_idx not in config_data_effective[config_type][model_type]:
            config_data_effective[config_type][model_type][dataset_idx] = {}

        # 提取permutation importance
        if 'importance' in result and 'permutation' in result['importance']:
            perm_imp = result['importance']['permutation']
            config_data[config_type][model_type][dataset_idx].update(perm_imp)

            # 如果是有效数据集，也添加到有效数据集
            if config_type in effective_datasets and dataset_name in effective_datasets[config_type]:
                config_data_effective[config_type][model_type][dataset_idx].update(perm_imp)

    if not config_data:
        print("没有有效的配置数据")
        return

    # 定义模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 为每个配置生成单独的图
    for config_type in sorted(config_data.keys()):
        config_name = config_type
        if custom_functions_configs and config_type in custom_functions_configs:
            config_name = custom_functions_configs[config_type]['name']

        # print(f"生成配置 {config_name} 的按节点类型分组的特征重要性图...")

        # 收集该配置下所有数据集的节点类型信息
        node_type_data = collect_node_type_data_for_config(
            config_type, config_data[config_type], scm_objects, preferred_model_order
        )

        # 收集该配置下有效数据集的节点类型信息
        node_type_data_effective = None
        if config_type in config_data_effective:
            node_type_data_effective = collect_node_type_data_for_config(
                config_type, config_data_effective[config_type], scm_objects, preferred_model_order
            )

        if not node_type_data:
            print(f"配置 {config_name} 没有有效的节点类型数据，跳过")
            continue

        # 生成两个子图的可视化
        plot_config_node_type_importance(node_type_data, config_name, image_dir,
                                        node_type_data_effective, effective_datasets, config_type,
                                        annotate_all_datasets, annotate_effective_datasets)

    print("已完成所有配置的按节点类型分组的特征重要性可视化")

def plot_config_node_type_importance(node_type_data, config_name, image_dir,
                                    node_type_data_effective=None, effective_datasets=None, config_type=None,
                                    annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    为单个配置绘制按节点类型分组的特征重要性图
    分别保存两个独立的图：马尔科夫 vs 其他，以及详细的四种类型

    Args:
        node_type_data: 所有数据集的节点类型数据
        config_name: 配置名称
        image_dir: 图像保存目录
        node_type_data_effective: 有效数据集的节点类型数据
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    markov_data = node_type_data['markov_vs_others']
    detailed_data = node_type_data['detailed_types']

    # 获取有效数据集的数据（如果提供）
    markov_data_effective = node_type_data_effective['markov_vs_others'] if node_type_data_effective else {}
    detailed_data_effective = node_type_data_effective['detailed_types'] if node_type_data_effective else {}

    # 获取可用的模型类型
    available_models = list(markov_data.keys())
    if not available_models:
        return

    # 修复文件名生成逻辑，保留小数点并替换为下划线，避免文件名冲突
    safe_config_name = "".join(c if c.isalnum() or c in (' ', '-', '_') else '_' if c == '.' else '' for c in config_name).rstrip()

    # 图1: 马尔科夫节点 vs 其他节点 - 独立画布
    plot_node_type_standalone(markov_data, available_models, config_name, safe_config_name, image_dir,
                              markov_data_effective, effective_datasets, config_type,
                              annotate_all_datasets, annotate_effective_datasets, plot_type='markov')

    # 图2: 详细的四种节点类型 - 独立画布
    plot_node_type_standalone(detailed_data, available_models, config_name, safe_config_name, image_dir,
                              detailed_data_effective, effective_datasets, config_type,
                              annotate_all_datasets, annotate_effective_datasets, plot_type='detailed')

def plot_node_type_standalone(data, available_models, config_name, safe_config_name, image_dir,
                             data_effective=None, effective_datasets=None, config_type=None,
                             annotate_all_datasets=True, annotate_effective_datasets=True, plot_type='markov'):
    """
    绘制节点类型分组的独立图

    Args:
        data: 节点类型数据
        available_models: 可用模型列表
        config_name: 配置名称
        safe_config_name: 安全的配置名称（用于文件名）
        image_dir: 图像保存目录
        data_effective: 有效数据集的节点类型数据
        effective_datasets: 有效数据集字典
        config_type: 配置类型
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
        plot_type: 绘图类型，'markov' 或 'detailed'
    """
    # 根据绘图类型配置参数
    if plot_type == 'markov':
        node_categories = ['markov', 'others']
        colors = {'markov': 'lightcoral', 'others': 'lightblue'}
        base_width = 3
        width_multiplier = 1.5
        min_width = 8
        base_box_spacing = 1.2
        spacing_reduction = 0.05
        base_box_width = 0.8
        width_reduction = 0.02
        font_size_base = 9
        font_size_reduction = 0.2
        model_font_size_base = 12
        model_font_size_reduction = 0.3
        y_offset_factor = 0.04
        title_suffix = 'Markov vs Others'
        filename_suffix = 'markov_vs_others'
        categories_per_model = 2
    else:  # detailed
        node_categories = ['parents', 'children', 'spouses', 'others']
        colors = {'parents': 'lightcoral', 'children': 'lightgreen', 'spouses': 'plum', 'others': 'lightblue'}
        base_width = 4
        width_multiplier = 2.0
        min_width = 12
        base_box_spacing = 1.0
        spacing_reduction = 0.03
        base_box_width = 0.6
        width_reduction = 0.015
        font_size_base = 8
        font_size_reduction = 0.15
        model_font_size_base = 11
        model_font_size_reduction = 0.25
        y_offset_factor = 0.06
        title_suffix = 'Detailed Node Types'
        filename_suffix = 'detailed_types'
        categories_per_model = 4

    # 动态计算图形尺寸，适应更多模型
    num_models = len(available_models)
    fig_width = max(min_width, base_width + num_models * width_multiplier)
    fig_height = 8

    fig, ax = plt.subplots(1, 1, figsize=(fig_width, fig_height))

    all_box_data = []
    all_box_labels = []
    all_box_colors = []

    # 记录每个模型的位置范围和分隔线位置
    model_positions = {}
    separator_positions = []
    current_position = 1

    # 增加箱子之间的间距，适应更多模型
    box_spacing = max(0.6, base_box_spacing - num_models * spacing_reduction)

    for i, model_type in enumerate(available_models):
        model_data = data[model_type]
        start_pos = current_position

        for node_category in node_categories:
            if model_data[node_category]:  # 只有当有数据时才添加
                all_box_data.append(model_data[node_category])
                all_box_labels.append(node_category)  # 只显示节点类型
                all_box_colors.append(colors[node_category])
                current_position += box_spacing  # 使用动态间距

        if current_position > start_pos:
            model_positions[model_type] = (start_pos, current_position - box_spacing)
            # 添加分隔线位置（除了最后一个模型）
            if i < len(available_models) - 1:
                separator_positions.append(current_position - box_spacing/2)

    if all_box_data:
        # 动态调整箱子宽度，适应更多模型
        box_width = max(0.3, base_box_width - num_models * width_reduction)

        # 绘制箱线图，增加箱子之间的间距
        positions = [i * box_spacing + 1 for i in range(len(all_box_data))]
        bp = ax.boxplot(all_box_data, positions=positions, labels=all_box_labels, patch_artist=True,
                       showmeans=True, meanline=True, widths=box_width)

        # 设置颜色
        for patch, color in zip(bp['boxes'], all_box_colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        # 计算所有数据的最大值，用于确定注释位置
        all_max_values = []
        for values in all_box_data:
            if values:
                all_max_values.append(np.max(values))

        if all_max_values:
            global_max = max(all_max_values)
            # 为注释预留适中的空间
            annotation_offset = global_max * 0.12

            # 添加均值和标准差标注，避免与分隔线和纵轴重叠
            for k, values in enumerate(all_box_data):
                if values:
                    max_val = np.max(values)

                    # 检查是否靠近分隔线，如果是则稍微偏移x位置
                    x_pos = positions[k]
                    for sep_pos in separator_positions:
                        if abs(x_pos - sep_pos) < box_spacing * 0.4:  # 如果靠近分隔线
                            if x_pos < sep_pos:
                                x_pos -= box_spacing * 0.1  # 向左偏移
                            else:
                                x_pos += box_spacing * 0.1  # 向右偏移

                    # 动态调整字体大小
                    font_size = max(6, font_size_base - num_models * font_size_reduction)

                    # 标注所有数据集的统计信息
                    if annotate_all_datasets:
                        mean_val = np.mean(values)
                        std_val = np.std(values)
                        y_pos = max_val + annotation_offset * 0.4
                        ax.text(x_pos, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                               ha='center', va='bottom', fontsize=font_size, color='blue',
                               bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.95, edgecolor='lightgray'))

                    # 标注有效数据集的统计信息
                    if annotate_effective_datasets and data_effective:
                        # 获取对应的有效数据集数据
                        model_idx = k // categories_per_model
                        node_category_idx = k % categories_per_model
                        node_category = node_categories[node_category_idx]

                        if (model_idx < len(available_models) and
                            available_models[model_idx] in data_effective and
                            node_category in data_effective[available_models[model_idx]]):

                            eff_values = data_effective[available_models[model_idx]][node_category]
                            if eff_values:
                                eff_mean_val = np.mean(eff_values)
                                eff_std_val = np.std(eff_values)
                                # 有效数据集标注位置稍微上移
                                eff_y_pos = max_val + annotation_offset * (0.8 if annotate_all_datasets else 0.4)
                                ax.text(x_pos, eff_y_pos, f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                       ha='center', va='bottom', fontsize=font_size-1, color='red',
                                       bbox=dict(boxstyle='round,pad=0.1', facecolor='lightyellow', alpha=0.95, edgecolor='orange'))

            # 调整y轴上限以容纳注释（考虑双重标注的情况）
            current_ylim = ax.get_ylim()
            # 如果同时标注两种数据，需要更多空间
            multiplier = 1.4 if (annotate_all_datasets and annotate_effective_datasets) else 1.0
            new_ymax = global_max + annotation_offset * multiplier
            ax.set_ylim(current_ylim[0], max(current_ylim[1], new_ymax))

        # 绘制垂直分隔线，顶着上方横轴
        y_min, y_max = ax.get_ylim()
        for sep_pos in separator_positions:
            ax.axvline(x=sep_pos, ymin=0, ymax=1, color='gray', linestyle='--', linewidth=1.2, alpha=0.6)

        # 在底部添加模型名称
        for model_type, (start, end) in model_positions.items():
            center_x = (start + end) / 2
            # 动态调整模型名称字体大小和位置
            model_font_size = max(7, model_font_size_base - num_models * model_font_size_reduction)
            ax.text(center_x, y_min - (y_max - y_min) * y_offset_factor, model_type,
                   ha='center', va='top', fontsize=model_font_size, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.15', facecolor='lightgray', alpha=0.7))

    ax.set_ylabel('Permutation Importance', fontsize=12)
    # 在标题中添加有效数据集个数信息
    title = f'{config_name} - {title_suffix}'
    if annotate_effective_datasets and effective_datasets and config_type and config_type in effective_datasets:
        eff_count = len(effective_datasets[config_type])
        title += f' (Eff: {eff_count})'
    ax.set_title(title, fontsize=13, fontweight='bold', pad=15)
    ax.grid(True, alpha=0.3)

    # 动态调整x轴标签
    if plot_type == 'detailed':
        # 详细类型需要旋转标签
        label_font_size = max(6, font_size_base - num_models * font_size_reduction)
        rotation_angle = min(45, 15 + num_models * 2)  # 模型越多，旋转角度越大
        plt.setp(ax.get_xticklabels(), rotation=rotation_angle, ha='right', fontsize=label_font_size)
    else:
        # 马尔科夫类型不需要旋转
        label_font_size = max(7, 10 - num_models * 0.2)
        ax.tick_params(axis='x', labelsize=label_font_size)

    # 增加图的边距
    ax.margins(x=0.02, y=0.05)

    # 保存图像
    filename = f'permutation_importance_{filename_suffix}_{safe_config_name}.png'
    plt.tight_layout(pad=1.5)
    plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()




#-------------------------------------------------------------model evaluation---------------------------------------------------
def calculate_effective_datasets(results, r2_threshold=0.5, outlier_threshold=3.0):
    """
    基于R2阈值和异常值检测计算有效数据集

    Args:
        results: 模型结果列表，每个元素包含dataset, model_type, use_snr, r2_train等字段
        r2_threshold: R2阈值，默认0.5
        outlier_threshold: 异常值检测阈值（Z分数），默认3.0

    Returns:
        dict: {config_key: {dataset_name: {model_type: r2_train_value}}} 格式的有效数据集信息
        dict: {config_key: set(effective_dataset_names)} 格式的有效数据集集合
    """
    

    # 按配置分组收集数据
    config_data = {}  # {config_key: {dataset_name: {model_type: r2_train}}}

    for result in results:
        config_key = result.get('use_snr', 'unknown')
        dataset_name = result.get('dataset', 'unknown')
        model_type = result.get('model_type', 'unknown')
        r2_train = result.get('r2_train', None)  # 保持None值以便后续处理

        if config_key not in config_data:
            config_data[config_key] = {}
        if dataset_name not in config_data[config_key]:
            config_data[config_key][dataset_name] = {}

        config_data[config_key][dataset_name][model_type] = r2_train

    # 计算每个配置下的有效数据集
    effective_datasets = {}  # {config_key: set(effective_dataset_names)}

    print(f"DEBUG: 计算有效数据集，R2阈值={r2_threshold}")

    for config_key, datasets in config_data.items():
        effective_datasets[config_key] = set()
        print(f"DEBUG: 处理配置 {config_key}")

        # 为当前配置创建DataFrame以便进行统计分析
        dataset_records = []
        for dataset_name, models in datasets.items():
            record = {
                'dataset': dataset_name,
                'xgb_r2': models.get('xgboost', None),
                'tabpfn_default_r2': models.get('tabpfn_default', None),
                'tabpfn_mse_r2': models.get('tabpfn_mse', None),
                'tabpfn_muzero_r2': models.get('tabpfn_muzero', None)
            }
            dataset_records.append(record)

        if not dataset_records:
            continue

        df = pd.DataFrame(dataset_records)

        # 首先过滤掉包含None值的行
        # 检查XGBoost是否有有效值
        xgb_valid_mask = df['xgb_r2'].notna()

        # 检查TabPFN是否有任何有效值（不为None且大于0.001）
        tabpfn_valid_mask = (
            (df['tabpfn_default_r2'].notna() & (df['tabpfn_default_r2'] > 0.001)) |
            (df['tabpfn_mse_r2'].notna() & (df['tabpfn_mse_r2'] > 0.001)) |
            (df['tabpfn_muzero_r2'].notna() & (df['tabpfn_muzero_r2'] > 0.001))
        )

        # 计算每个数据集的最佳TabPFN R2值
        df['best_tabpfn_r2'] = df[['tabpfn_default_r2', 'tabpfn_mse_r2', 'tabpfn_muzero_r2']].max(axis=1, skipna=True)

        # 处理没有有效TabPFN结果的情况
        df.loc[~tabpfn_valid_mask, 'best_tabpfn_r2'] = None

        print(f"DEBUG: 总数据集数: {len(df)}")
        print(f"DEBUG: XGBoost有效数据集数: {xgb_valid_mask.sum()}")
        print(f"DEBUG: TabPFN有效数据集数: {tabpfn_valid_mask.sum()}")

        # 对于有TabPFN结果的数据集，要求两个模型都满足阈值
        both_valid_mask = xgb_valid_mask & tabpfn_valid_mask
        both_valid_df = df[both_valid_mask]

        if len(both_valid_df) > 0:
            both_r2_mask = (both_valid_df['xgb_r2'] > r2_threshold) & (both_valid_df['best_tabpfn_r2'] > r2_threshold)
            both_effective = both_valid_df[both_r2_mask]['dataset'].tolist()
            effective_datasets[config_key].update(both_effective)
            print(f"DEBUG: 双模型有效数据集: {both_effective}")

        # 对于只有XGBoost结果的数据集，只检查XGBoost
        only_xgb_mask = xgb_valid_mask & ~tabpfn_valid_mask
        only_xgb_df = df[only_xgb_mask]

        if len(only_xgb_df) > 0:
            xgb_r2_mask = only_xgb_df['xgb_r2'] > r2_threshold
            xgb_effective = only_xgb_df[xgb_r2_mask]['dataset'].tolist()
            effective_datasets[config_key].update(xgb_effective)
            print(f"DEBUG: 仅XGBoost有效数据集: {xgb_effective}")

        print(f"DEBUG: 配置 {config_key} 的有效数据集: {list(effective_datasets[config_key])}")

    return config_data, effective_datasets


def plot_r2_comparison(r2_results, image_dir,
                      r2_threshold=0.5, annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    自适应R²性能对比图生成函数

    Args:
        r2_results: R²性能结果列表（这里实际上是完整的results，包含model_type和dataset信息）
        image_dir: 图片保存目录
        r2_threshold: R2阈值，用于筛选有效数据集
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    if not r2_results:
        return

    # 检查是否是custom_functions模式
    is_custom_functions_mode = isinstance(r2_results[0]['use_snr'], str)

    if not is_custom_functions_mode:
        return

    # Custom functions模式：按数据集和模型分组
    datasets = {}
    config_types = set()

    for r in r2_results:
        dataset_name = r['dataset']
        model_type = r['model_type']
        config_type = r['use_snr']  # 现在是配置类型字符串
        config_types.add(config_type)

        if dataset_name not in datasets:
            datasets[dataset_name] = {}
        if model_type not in datasets[dataset_name]:
            datasets[dataset_name][model_type] = {}

        datasets[dataset_name][model_type][config_type] = r

    config_types = sorted(list(config_types))

    # 计算有效数据集
    effective_datasets = {}
    _, effective_datasets = calculate_effective_datasets(r2_results, r2_threshold)

    # 生成按预测模型分组的图
    plot_custom_functions_summary(datasets, config_types, image_dir, suffix='_merged',
                                 effective_datasets=effective_datasets,
                                 annotate_all_datasets=annotate_all_datasets,
                                 annotate_effective_datasets=annotate_effective_datasets,
                                 group_by='model')

    # 生成按SNR配置分组的图
    plot_custom_functions_summary(datasets, config_types, image_dir, suffix='_merged',
                                 effective_datasets=effective_datasets,
                                 annotate_all_datasets=annotate_all_datasets,
                                 annotate_effective_datasets=annotate_effective_datasets,
                                 group_by='config')


def plot_custom_functions_summary(datasets, config_types, image_dir, suffix='',
                                 effective_datasets=None, annotate_all_datasets=True,
                                 annotate_effective_datasets=True, group_by='model'):
    """
    R²汇总箱线图生成函数

    Args:
        datasets: 数据集字典
        config_types: 配置类型列表
        image_dir: 图片保存目录
        suffix: 文件名后缀
        effective_datasets: 有效数据集字典 {config_key: set(effective_dataset_names)}
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
        group_by: 分组方式，'model' 或 'config'
                 'model': 按预测模型分组，为每个预测模型画一个图，横轴是SNR配置
                 'config': 按SNR配置分组，为每个SNR配置画一个图，横轴是预测模型
    """
    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]

    if not model_types:
        return

    metrics = ['r2_train', 'r2_test', 'r2_intv']
    metric_names = ['Train R²', 'Test R²', 'Intervention R²']

    # 根据分组方式确定外层循环
    if group_by == 'model':
        outer_items = model_types
    else:  # group_by == 'config'
        outer_items = config_types

    for outer_item in outer_items:
        plt.figure(figsize=(18, 6))

        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            ax = plt.subplot(1, 3, i + 1)

            if group_by == 'model':
                # 按模型分组：收集当前模型在每个配置下的数据
                model_type = outer_item
                data_dict = {config: [] for config in config_types}

                for models in datasets.values():
                    if model_type in models:
                        model_data = models[model_type]
                        for config_type in config_types:
                            if config_type in model_data:
                                data_dict[config_type].append(model_data[config_type][metric])

                # 准备箱线图数据
                box_data = []
                box_labels = []
                label_items = []

                for config_type in config_types:
                    if data_dict[config_type]:
                        box_data.append(data_dict[config_type])
                        box_labels.append(config_type)
                        label_items.append(config_type)

            else:  # group_by == 'config'
                # 按配置分组：收集当前配置下每个模型的数据
                config_type = outer_item
                data_dict = {model: [] for model in model_types}

                for models in datasets.values():
                    for model_type in model_types:
                        if model_type in models and config_type in models[model_type]:
                            data_dict[model_type].append(models[model_type][config_type][metric])

                # 准备箱线图数据
                box_data = []
                box_labels = []
                label_items = []

                for model_type in model_types:
                    if data_dict[model_type]:
                        box_data.append(data_dict[model_type])
                        box_labels.append(model_type.upper())
                        label_items.append(model_type)

            if box_data:
                # 绘制箱线图
                bp = ax.boxplot(box_data, labels=box_labels, patch_artist=True, showmeans=True)

                # 设置颜色
                colors = ['lightblue', 'lightcoral', 'lightgreen', 'plum', 'lightyellow', 'lightpink']
                for j, patch in enumerate(bp['boxes']):
                    patch.set_facecolor(colors[j % len(colors)])
                    patch.set_alpha(0.7)

                # 计算全局最大值用于确定注释位置
                all_max_values = [np.max(values) for values in box_data if values]
                if all_max_values:
                    global_max = max(all_max_values)
                    # 使用与特征重要性图相同的注释偏移量计算方式
                    annotation_offset = global_max * 0.12

                # 添加双重标注：所有数据集 + 有效数据集
                for j, values in enumerate(box_data):
                    if values:
                        label_item = label_items[j]
                        max_val = np.max(values)

                        # 标注所有数据集的统计信息
                        if annotate_all_datasets:
                            mean_val = np.mean(values)
                            std_val = np.std(values)
                            y_pos = max_val + annotation_offset * 0.4
                            ax.text(j+1, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                                   ha='center', va='bottom', fontsize=7, color='blue',
                                   bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.95, edgecolor='lightgray'))

                        # 标注有效数据集的统计信息
                        if annotate_effective_datasets and effective_datasets:
                            # 收集有效数据集的数据
                            effective_values = []

                            if group_by == 'model':
                                # 按模型分组时，label_item是config_type
                                config_type = label_item
                                if config_type in effective_datasets and effective_datasets[config_type]:
                                    effective_dataset_names = effective_datasets[config_type]
                                    for dataset_name in datasets:
                                        if (dataset_name in effective_dataset_names and
                                            outer_item in datasets[dataset_name] and
                                            config_type in datasets[dataset_name][outer_item]):
                                            effective_values.append(datasets[dataset_name][outer_item][config_type][metric])
                            else:
                                # 按配置分组时，label_item是model_type，outer_item是config_type
                                model_type = label_item
                                config_type = outer_item
                                if config_type in effective_datasets and effective_datasets[config_type]:
                                    effective_dataset_names = effective_datasets[config_type]
                                    for dataset_name in datasets:
                                        if (dataset_name in effective_dataset_names and
                                            model_type in datasets[dataset_name] and
                                            config_type in datasets[dataset_name][model_type]):
                                            effective_values.append(datasets[dataset_name][model_type][config_type][metric])

                            if effective_values:
                                eff_mean_val = np.mean(effective_values)
                                eff_std_val = np.std(effective_values)
                                # 有效数据集标注位置稍微上移，避免重叠
                                eff_y_pos = max_val + annotation_offset * (0.8 if annotate_all_datasets else 0.4)
                                ax.text(j+1, eff_y_pos, f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                       ha='center', va='bottom', fontsize=6, color='red',
                                       bbox=dict(boxstyle='round,pad=0.1', facecolor='lightyellow', alpha=0.95, edgecolor='orange'))

                # 调整y轴上限以容纳注释
                if all_max_values:
                    current_ylim = ax.get_ylim()
                    multiplier = 1.4 if (annotate_all_datasets and annotate_effective_datasets) else 1.0
                    new_ymax = global_max + annotation_offset * multiplier
                    ax.set_ylim(current_ylim[0], max(current_ylim[1], new_ymax))

            ax.set_ylabel('R² Score')
            ax.set_title(f'{metric_name} Comparison')
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0, 1.1)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), ha='center')

        # 设置总标题，在这里添加有效数据集个数信息
        title_suffix = suffix.replace('_', ' ').title() if suffix else ''

        if group_by == 'model':
            main_title = f'{outer_item.upper()} Performance Across SNR Configurations{title_suffix}'
            if annotate_effective_datasets and effective_datasets:
                total_effective = sum(len(datasets) for datasets in effective_datasets.values())
                main_title += f' (Eff: {total_effective})'
            filename = f'custom_functions_r2_by_model_{outer_item}{suffix}.png'
        else:  # group_by == 'config'
            config_display = outer_item.replace('_', ' ')
            main_title = f'Model Performance Comparison - {config_display}{title_suffix}'
            if annotate_effective_datasets and effective_datasets and outer_item in effective_datasets:
                eff_count = len(effective_datasets[outer_item])
                main_title += f' (Eff: {eff_count})'
            safe_config_name = outer_item.replace('.', '_').replace(' ', '_')
            filename = f'custom_functions_r2_by_config_{safe_config_name}{suffix}.png'

        plt.suptitle(main_title, fontsize=16, weight='bold')
        plt.tight_layout()

        # 保存文件
        plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
        plt.close()





